#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeoNames数据下载器
用于批量下载GeoNames地理数据
"""

import os
import requests
import zipfile
import time
from urllib.parse import urljoin
import pandas as pd

class GeoNamesDownloader:
    """GeoNames数据下载器"""
    
    def __init__(self, base_url="https://download.geonames.org/export/dump/"):
        self.base_url = base_url
        self.download_dir = "geonames_data"
        
        # 创建下载目录
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
    
    def get_country_list(self):
        """获取所有可用的国家代码列表"""
        countries = {
            # 亚洲主要国家
            'CN': '中国', 'JP': '日本', 'KR': '韩国', 'IN': '印度',
            'TH': '泰国', 'VN': '越南', 'MY': '马来西亚', 'SG': '新加坡',
            'ID': '印度尼西亚', 'PH': '菲律宾', 'HK': '香港', 'TW': '台湾',
            'MO': '澳门',
            
            # 欧洲主要国家
            'DE': '德国', 'FR': '法国', 'GB': '英国', 'IT': '意大利',
            'ES': '西班牙', 'NL': '荷兰', 'BE': '比利时', 'CH': '瑞士',
            'AT': '奥地利', 'SE': '瑞典', 'NO': '挪威', 'DK': '丹麦',
            'FI': '芬兰', 'PL': '波兰', 'RU': '俄罗斯',
            
            # 北美洲
            'US': '美国', 'CA': '加拿大', 'MX': '墨西哥',
            
            # 南美洲
            'BR': '巴西', 'AR': '阿根廷', 'CL': '智利', 'PE': '秘鲁',
            
            # 大洋洲
            'AU': '澳大利亚', 'NZ': '新西兰',
            
            # 非洲主要国家
            'ZA': '南非', 'EG': '埃及', 'NG': '尼日利亚', 'KE': '肯尼亚'
        }
        return countries
    
    def download_file(self, filename, show_progress=True):
        """下载单个文件"""
        url = urljoin(self.base_url, filename)
        local_path = os.path.join(self.download_dir, filename)
        
        # 检查文件是否已存在
        if os.path.exists(local_path):
            print(f"文件 {filename} 已存在，跳过下载")
            return local_path
        
        try:
            print(f"开始下载: {filename}")
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if show_progress and total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
            
            if show_progress:
                print(f"\n✓ 下载完成: {filename}")
            
            return local_path
            
        except Exception as e:
            print(f"✗ 下载失败 {filename}: {e}")
            return None
    
    def extract_zip(self, zip_path):
        """解压ZIP文件"""
        try:
            extract_dir = os.path.splitext(zip_path)[0]
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.download_dir)
            
            print(f"✓ 解压完成: {os.path.basename(zip_path)}")
            return True
            
        except Exception as e:
            print(f"✗ 解压失败 {zip_path}: {e}")
            return False
    
    def download_country(self, country_code, extract=True):
        """下载指定国家的数据"""
        filename = f"{country_code.upper()}.zip"
        
        # 下载文件
        zip_path = self.download_file(filename)
        
        if zip_path and extract:
            # 解压文件
            self.extract_zip(zip_path)
        
        return zip_path
    
    def download_multiple_countries(self, country_codes, extract=True, delay=1):
        """批量下载多个国家的数据"""
        results = {}
        
        for i, country_code in enumerate(country_codes):
            print(f"\n[{i+1}/{len(country_codes)}] 处理国家: {country_code}")
            
            result = self.download_country(country_code, extract)
            results[country_code] = result
            
            # 添加延迟避免服务器压力
            if delay > 0 and i < len(country_codes) - 1:
                time.sleep(delay)
        
        return results
    
    def download_special_files(self):
        """下载特殊文件"""
        special_files = [
            'readme.txt',           # 说明文件
            'countryInfo.txt',      # 国家信息
            'timeZones.txt',        # 时区信息
            'featureCodes_en.txt',  # 特征代码说明
            'admin1CodesASCII.txt', # 一级行政区代码
            'cities1000.zip',       # 人口>1000的城市
            'cities5000.zip',       # 人口>5000的城市
            'cities15000.zip'       # 人口>15000的城市
        ]
        
        results = {}
        for filename in special_files:
            print(f"\n下载特殊文件: {filename}")
            result = self.download_file(filename)
            results[filename] = result
            
            # 如果是ZIP文件，自动解压
            if filename.endswith('.zip') and result:
                self.extract_zip(result)
        
        return results
    
    def get_file_info(self):
        """获取下载目录中的文件信息"""
        files_info = []
        
        for filename in os.listdir(self.download_dir):
            filepath = os.path.join(self.download_dir, filename)
            if os.path.isfile(filepath):
                size = os.path.getsize(filepath)
                files_info.append({
                    'filename': filename,
                    'size_mb': size / (1024 * 1024),
                    'type': 'ZIP' if filename.endswith('.zip') else 'TXT'
                })
        
        return sorted(files_info, key=lambda x: x['size_mb'], reverse=True)

def main():
    """主函数 - 演示用法"""
    downloader = GeoNamesDownloader()
    
    print("GeoNames数据下载器")
    print("=" * 50)
    
    # 显示可用国家
    countries = downloader.get_country_list()
    print("\n可用国家列表:")
    for code, name in list(countries.items())[:10]:  # 显示前10个
        print(f"  {code}: {name}")
    print("  ... 更多国家")
    
    # 用户选择
    print("\n请选择操作:")
    print("1. 下载单个国家数据")
    print("2. 下载多个国家数据")
    print("3. 下载特殊文件")
    print("4. 查看已下载文件")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == '1':
        # 下载单个国家
        country_code = input("请输入国家代码 (如 CN, US, JP): ").strip().upper()
        if country_code in countries:
            print(f"\n开始下载 {countries[country_code]} ({country_code}) 的数据...")
            downloader.download_country(country_code)
        else:
            print("无效的国家代码")
    
    elif choice == '2':
        # 下载多个国家
        codes_input = input("请输入国家代码，用逗号分隔 (如 CN,JP,KR): ").strip()
        country_codes = [code.strip().upper() for code in codes_input.split(',')]
        
        valid_codes = [code for code in country_codes if code in countries]
        if valid_codes:
            print(f"\n开始下载 {len(valid_codes)} 个国家的数据...")
            downloader.download_multiple_countries(valid_codes)
        else:
            print("没有有效的国家代码")
    
    elif choice == '3':
        # 下载特殊文件
        print("\n开始下载特殊文件...")
        downloader.download_special_files()
    
    elif choice == '4':
        # 查看已下载文件
        files_info = downloader.get_file_info()
        if files_info:
            print("\n已下载的文件:")
            print(f"{'文件名':<30} {'大小(MB)':<10} {'类型':<5}")
            print("-" * 50)
            for info in files_info:
                print(f"{info['filename']:<30} {info['size_mb']:<10.2f} {info['type']:<5}")
        else:
            print("\n没有已下载的文件")
    
    else:
        print("无效的选择")

if __name__ == "__main__":
    main()
