#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全球业务友好数据生成器
将原始GeoNames数据转换为业务友好的格式
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
import os
from collections import defaultdict

class BusinessFriendlyDataGenerator:
    """业务友好数据生成器"""
    
    def __init__(self):
        self.feature_codes = {}
        self.admin_codes = {}
        self.country_info = {}
        self.timezone_info = {}
        
    def load_auxiliary_data(self):
        """加载所有辅助数据"""
        print("🔄 加载辅助数据...")
        
        # 1. 加载特征代码说明
        try:
            feature_df = pd.read_csv('featureCodes_en.txt', sep='\t', 
                                   names=['code', 'name', 'description'], 
                                   encoding='utf-8')
            for _, row in feature_df.iterrows():
                self.feature_codes[row['code']] = {
                    'name': row['name'],
                    'description': row['description']
                }
            print(f"✅ 特征代码: {len(self.feature_codes)} 个")
        except Exception as e:
            print(f"⚠️ 特征代码加载失败: {e}")
        
        # 2. 加载行政区代码
        try:
            admin_df = pd.read_csv('admin1CodesASCII.txt', sep='\t',
                                 names=['code', 'name', 'asciiname', 'geonameid'],
                                 encoding='utf-8')
            for _, row in admin_df.iterrows():
                self.admin_codes[row['code']] = {
                    'name': row['name'],
                    'ascii_name': row['asciiname'],
                    'geonameid': row['geonameid']
                }
            print(f"✅ 行政区代码: {len(self.admin_codes)} 个")
        except Exception as e:
            print(f"⚠️ 行政区代码加载失败: {e}")
        
        # 3. 加载国家信息
        try:
            country_data = []
            with open('countryInfo.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.startswith('#') and line.strip():
                        parts = line.strip().split('\t')
                        if len(parts) >= 16:
                            country_data.append(parts)
            
            for parts in country_data:
                iso_code = parts[0]
                self.country_info[iso_code] = {
                    'iso': parts[0],
                    'iso3': parts[1] if len(parts) > 1 else '',
                    'iso_numeric': parts[2] if len(parts) > 2 else '',
                    'fips': parts[3] if len(parts) > 3 else '',
                    'country_name': parts[4] if len(parts) > 4 else '',
                    'capital': parts[5] if len(parts) > 5 else '',
                    'area_sqkm': parts[6] if len(parts) > 6 else '',
                    'population': parts[7] if len(parts) > 7 else '',
                    'continent': parts[8] if len(parts) > 8 else '',
                    'tld': parts[9] if len(parts) > 9 else '',
                    'currency_code': parts[10] if len(parts) > 10 else '',
                    'currency_name': parts[11] if len(parts) > 11 else '',
                    'phone_prefix': parts[12] if len(parts) > 12 else '',
                    'postal_format': parts[13] if len(parts) > 13 else '',
                    'postal_regex': parts[14] if len(parts) > 14 else '',
                    'languages': parts[15] if len(parts) > 15 else '',
                    'geonameid': parts[16] if len(parts) > 16 else '',
                    'neighbours': parts[17] if len(parts) > 17 else ''
                }
            print(f"✅ 国家信息: {len(self.country_info)} 个")
        except Exception as e:
            print(f"⚠️ 国家信息加载失败: {e}")
        
        # 4. 加载时区信息
        try:
            timezone_df = pd.read_csv('timeZones.txt', sep='\t',
                                    names=['country_code', 'timezone_id', 'gmt_offset', 'dst_offset', 'raw_offset'],
                                    encoding='utf-8')
            for _, row in timezone_df.iterrows():
                key = f"{row['country_code']}_{row['timezone_id']}"
                self.timezone_info[key] = {
                    'timezone_id': row['timezone_id'],
                    'gmt_offset': row['gmt_offset'],
                    'dst_offset': row['dst_offset'],
                    'raw_offset': row['raw_offset']
                }
            print(f"✅ 时区信息: {len(self.timezone_info)} 个")
        except Exception as e:
            print(f"⚠️ 时区信息加载失败: {e}")
    
    def get_continent_name(self, continent_code):
        """获取大洲中文名称"""
        continent_map = {
            'AF': '非洲',
            'AS': '亚洲', 
            'EU': '欧洲',
            'NA': '北美洲',
            'OC': '大洋洲',
            'SA': '南美洲',
            'AN': '南极洲'
        }
        return continent_map.get(continent_code, continent_code)
    
    def get_feature_info(self, feature_class, feature_code):
        """获取特征信息"""
        full_code = f"{feature_class}.{feature_code}"
        if full_code in self.feature_codes:
            return self.feature_codes[full_code]
        return {'name': feature_code, 'description': ''}
    
    def get_admin_name(self, country_code, admin_code):
        """获取行政区名称"""
        if pd.isna(admin_code) or admin_code == '' or admin_code == '00':
            return ''
        
        full_code = f"{country_code}.{admin_code}"
        if full_code in self.admin_codes:
            return self.admin_codes[full_code]['name']
        return admin_code
    
    def get_timezone_info(self, country_code, timezone_id):
        """获取时区信息"""
        key = f"{country_code}_{timezone_id}"
        if key in self.timezone_info:
            return self.timezone_info[key]
        return {'gmt_offset': 0, 'dst_offset': 0, 'raw_offset': 0}
    
    def format_population(self, population):
        """格式化人口数字"""
        if pd.isna(population) or population == 0:
            return ''
        
        if population >= 1000000:
            return f"{population/1000000:.1f}万"
        elif population >= 10000:
            return f"{population/10000:.1f}万"
        else:
            return f"{int(population):,}"
    
    def process_chunk(self, chunk, chunk_num):
        """处理数据块"""
        print(f"🔄 处理第 {chunk_num} 块数据，共 {len(chunk)} 条记录...")
        
        # 创建业务友好的数据结构
        business_data = []
        
        for idx, row in chunk.iterrows():
            try:
                # 获取国家信息
                country_info = self.country_info.get(row['country_code'], {})
                
                # 获取特征信息
                feature_info = self.get_feature_info(row['feature_class'], row['feature_code'])
                
                # 获取行政区名称
                admin1_name = self.get_admin_name(row['country_code'], row['admin1_code'])
                
                # 获取时区信息
                timezone_info = self.get_timezone_info(row['country_code'], row['timezone'])
                
                # 构建业务友好记录
                business_record = {
                    # 基本标识信息
                    'geoname_id': int(row['geonameid']),
                    'name': row['name'],
                    'ascii_name': row['asciiname'],
                    'alternate_names': row['alternatenames'] if pd.notna(row['alternatenames']) else '',
                    
                    # 地理位置信息
                    'latitude': float(row['latitude']),
                    'longitude': float(row['longitude']),
                    'coordinates': f"{row['latitude']:.6f}, {row['longitude']:.6f}",
                    
                    # 特征分类信息（业务友好）
                    'feature_class': row['feature_class'],
                    'feature_code': row['feature_code'],
                    'feature_name': feature_info['name'],
                    'feature_description': feature_info['description'],
                    'feature_category': self.get_feature_category(row['feature_class']),
                    
                    # 国家和地区信息（业务友好）
                    'country_code': row['country_code'],
                    'country_name': country_info.get('country_name', ''),
                    'country_name_cn': self.get_country_name_cn(row['country_code']),
                    'continent_code': country_info.get('continent', ''),
                    'continent_name': self.get_continent_name(country_info.get('continent', '')),
                    'capital': country_info.get('capital', ''),
                    'currency_code': country_info.get('currency_code', ''),
                    'currency_name': country_info.get('currency_name', ''),
                    'languages': country_info.get('languages', ''),
                    
                    # 行政区信息（业务友好）
                    'admin1_code': row['admin1_code'] if pd.notna(row['admin1_code']) else '',
                    'admin1_name': admin1_name,
                    'admin2_code': row['admin2_code'] if pd.notna(row['admin2_code']) else '',
                    'admin_full_path': self.build_admin_path(country_info.get('country_name', ''), admin1_name),
                    
                    # 人口和海拔信息（业务友好）
                    'population': int(row['population']) if pd.notna(row['population']) and row['population'] > 0 else 0,
                    'population_formatted': self.format_population(row['population']),
                    'elevation': int(row['elevation']) if pd.notna(row['elevation']) else None,
                    'elevation_formatted': f"{int(row['elevation'])}米" if pd.notna(row['elevation']) else '',
                    
                    # 时区信息（业务友好）
                    'timezone': row['timezone'] if pd.notna(row['timezone']) else '',
                    'gmt_offset': timezone_info.get('gmt_offset', 0),
                    'timezone_formatted': self.format_timezone(timezone_info.get('gmt_offset', 0)),
                    
                    # 业务标签和分类
                    'is_major_city': self.is_major_city(row),
                    'is_capital': self.is_capital(row),
                    'is_tourist_attraction': self.is_tourist_attraction(row),
                    'business_category': self.get_business_category(row),
                    'importance_score': self.calculate_importance_score(row),
                    
                    # 元数据
                    'data_source': 'GeoNames',
                    'last_modified': row['modification_date'] if pd.notna(row['modification_date']) else '',
                    'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                business_data.append(business_record)
                
            except Exception as e:
                print(f"⚠️ 处理记录 {row['geonameid']} 时出错: {e}")
                continue
        
        return pd.DataFrame(business_data)
    
    def get_feature_category(self, feature_class):
        """获取特征大类别"""
        category_map = {
            'A': '行政区域',
            'H': '水文地理', 
            'L': '区域地带',
            'P': '居住地点',
            'R': '交通设施',
            'S': '建筑景点',
            'T': '地形地貌',
            'U': '海底地形',
            'V': '植被覆盖'
        }
        return category_map.get(feature_class, '其他')
    
    def get_country_name_cn(self, country_code):
        """获取国家中文名称"""
        cn_names = {
            'CN': '中国', 'HK': '香港', 'TW': '台湾', 'MO': '澳门',
            'JP': '日本', 'KR': '韩国', 'US': '美国', 'GB': '英国',
            'FR': '法国', 'DE': '德国', 'IT': '意大利', 'ES': '西班牙',
            'CA': '加拿大', 'AU': '澳大利亚', 'BR': '巴西', 'IN': '印度',
            'RU': '俄罗斯', 'MX': '墨西哥', 'TH': '泰国', 'SG': '新加坡'
        }
        return cn_names.get(country_code, '')
    
    def build_admin_path(self, country_name, admin1_name):
        """构建完整行政路径"""
        parts = [part for part in [country_name, admin1_name] if part]
        return ' > '.join(parts)
    
    def format_timezone(self, gmt_offset):
        """格式化时区显示"""
        if gmt_offset == 0:
            return 'GMT'
        elif gmt_offset > 0:
            return f'GMT+{gmt_offset}'
        else:
            return f'GMT{gmt_offset}'
    
    def is_major_city(self, row):
        """判断是否为主要城市"""
        return (row['feature_code'] in ['PPLC', 'PPLA', 'PPLA2'] or 
                (pd.notna(row['population']) and row['population'] > 100000))
    
    def is_capital(self, row):
        """判断是否为首都"""
        return row['feature_code'] == 'PPLC'
    
    def is_tourist_attraction(self, row):
        """判断是否为旅游景点"""
        tourist_codes = ['HTL', 'MUS', 'TMPL', 'MNMT', 'AIRP', 'MTRO']
        return row['feature_code'] in tourist_codes
    
    def get_business_category(self, row):
        """获取业务分类"""
        if row['feature_class'] == 'P':
            if row['feature_code'] == 'PPLC':
                return '首都城市'
            elif row['feature_code'] in ['PPLA', 'PPLA2']:
                return '重要城市'
            else:
                return '一般城市'
        elif row['feature_class'] == 'S':
            if row['feature_code'] == 'HTL':
                return '酒店住宿'
            elif row['feature_code'] == 'AIRP':
                return '交通枢纽'
            else:
                return '服务设施'
        elif row['feature_class'] == 'T':
            return '自然景观'
        elif row['feature_class'] == 'H':
            return '水域地理'
        else:
            return '其他地点'
    
    def calculate_importance_score(self, row):
        """计算重要性评分"""
        score = 0
        
        # 基于特征代码的评分
        if row['feature_code'] == 'PPLC':
            score += 100  # 首都
        elif row['feature_code'] in ['PPLA', 'PPLA2']:
            score += 80   # 行政中心
        elif row['feature_code'] == 'AIRP':
            score += 70   # 机场
        
        # 基于人口的评分
        if pd.notna(row['population']):
            if row['population'] > 1000000:
                score += 50
            elif row['population'] > 100000:
                score += 30
            elif row['population'] > 10000:
                score += 10
        
        return min(score, 100)  # 最高100分
    
    def generate_business_friendly_data(self, input_file='allCountries.txt', chunk_size=100000):
        """生成业务友好数据"""
        print("🚀 开始生成全球业务友好数据")
        print("=" * 80)
        
        # 加载辅助数据
        self.load_auxiliary_data()
        
        # 定义列名
        columns = [
            'geonameid', 'name', 'asciiname', 'alternatenames',
            'latitude', 'longitude', 'feature_class', 'feature_code',
            'country_code', 'cc2', 'admin1_code', 'admin2_code',
            'admin3_code', 'admin4_code', 'population', 'elevation',
            'dem', 'timezone', 'modification_date'
        ]
        
        # 分块处理大文件
        output_file = 'global_business_friendly_data.csv'
        chunk_num = 0
        total_processed = 0
        
        try:
            for chunk in pd.read_csv(input_file, sep='\t', names=columns, 
                                   encoding='utf-8', chunksize=chunk_size, low_memory=False):
                chunk_num += 1
                
                # 处理当前块
                business_chunk = self.process_chunk(chunk, chunk_num)
                
                # 写入文件
                if chunk_num == 1:
                    business_chunk.to_csv(output_file, index=False, encoding='utf-8-sig')
                else:
                    business_chunk.to_csv(output_file, mode='a', header=False, index=False, encoding='utf-8-sig')
                
                total_processed += len(business_chunk)
                print(f"✅ 第 {chunk_num} 块处理完成，累计处理 {total_processed:,} 条记录")
                
                # 每10块输出一次进度
                if chunk_num % 10 == 0:
                    print(f"📊 进度更新: 已处理 {chunk_num} 块，共 {total_processed:,} 条记录")
        
        except Exception as e:
            print(f"❌ 处理过程中出错: {e}")
            return False
        
        print("=" * 80)
        print(f"🎉 全球业务友好数据生成完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 总记录数: {total_processed:,}")
        print(f"📦 处理块数: {chunk_num}")
        
        # 生成数据摘要
        self.generate_summary_report(output_file)
        
        return True

    def generate_summary_report(self, output_file):
        """生成数据摘要报告"""
        print("\n📊 生成数据摘要报告...")

        try:
            # 读取生成的业务友好数据进行统计
            sample_df = pd.read_csv(output_file, nrows=10000, encoding='utf-8-sig')

            # 创建摘要报告
            summary = {
                'generation_info': {
                    'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'output_file': output_file,
                    'data_source': 'GeoNames',
                    'processing_method': 'Business-Friendly Enhancement'
                },
                'data_structure': {
                    'total_columns': len(sample_df.columns),
                    'business_enhanced_fields': [
                        'feature_name', 'feature_description', 'feature_category',
                        'country_name', 'country_name_cn', 'continent_name',
                        'admin1_name', 'admin_full_path', 'population_formatted',
                        'elevation_formatted', 'timezone_formatted', 'business_category',
                        'importance_score'
                    ]
                },
                'sample_statistics': {
                    'feature_categories': sample_df['feature_category'].value_counts().to_dict(),
                    'business_categories': sample_df['business_category'].value_counts().to_dict(),
                    'top_countries': sample_df['country_name'].value_counts().head(10).to_dict(),
                    'continents': sample_df['continent_name'].value_counts().to_dict()
                }
            }

            # 保存摘要报告
            summary_file = 'business_friendly_data_summary.json'
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            print(f"✅ 摘要报告已保存: {summary_file}")

            # 输出关键统计信息
            print("\n📈 数据统计摘要:")
            print(f"  • 特征类别分布: {dict(list(summary['sample_statistics']['feature_categories'].items())[:5])}")
            print(f"  • 业务分类分布: {dict(list(summary['sample_statistics']['business_categories'].items())[:5])}")
            print(f"  • 主要国家分布: {dict(list(summary['sample_statistics']['top_countries'].items())[:5])}")

        except Exception as e:
            print(f"⚠️ 生成摘要报告时出错: {e}")

def main():
    """主函数"""
    generator = BusinessFriendlyDataGenerator()
    
    # 检查必要文件是否存在
    required_files = ['allCountries.txt', 'featureCodes_en.txt', 'admin1CodesASCII.txt', 
                     'countryInfo.txt', 'timeZones.txt']
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        print("请确保已下载所有辅助数据文件")
        return
    
    # 生成业务友好数据
    success = generator.generate_business_friendly_data()
    
    if success:
        print("\n🎯 业务友好数据已生成，可以用于:")
        print("  • 电商平台的地址验证和配送区域划分")
        print("  • 旅游应用的景点分类和路线规划") 
        print("  • 物流系统的区域规划和时效计算")
        print("  • 金融服务的合规检查和风险评估")
        print("  • 国际化应用的多语言和货币支持")

if __name__ == "__main__":
    main()
