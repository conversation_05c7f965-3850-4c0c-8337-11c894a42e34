#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeoNames多国数据分析脚本
分析中国(CN)、日本(JP)、香港(HK)和全球数据
"""

import pandas as pd
import numpy as np
from collections import Counter
import sys
import os

def analyze_geonames_data(filename):
    """
    分析GeoNames数据文件
    """
    country_name = {
        'HK.txt': '香港地区',
        'CN.txt': '中国',
        'JP.txt': '日本',
        'allCountries.txt': '全球'
    }.get(filename, filename.replace('.txt', ''))
    
    print("=" * 80)
    print(f"GeoNames {country_name}数据分析报告")
    print("=" * 80)
    
    # 根据readme.txt定义列名
    columns = [
        'geonameid',        # 地理名称ID
        'name',             # 地名（UTF-8）
        'asciiname',        # ASCII地名
        'alternatenames',   # 别名（逗号分隔）
        'latitude',         # 纬度（WGS84）
        'longitude',        # 经度（WGS84）
        'feature_class',    # 特征类别
        'feature_code',     # 特征代码
        'country_code',     # 国家代码（ISO-3166）
        'cc2',              # 备用国家代码
        'admin1_code',      # 一级行政区代码
        'admin2_code',      # 二级行政区代码
        'admin3_code',      # 三级行政区代码
        'admin4_code',      # 四级行政区代码
        'population',       # 人口
        'elevation',        # 海拔（米）
        'dem',              # 数字高程模型
        'timezone',         # 时区
        'modification_date' # 修改日期
    ]
    
    try:
        # 读取数据文件
        print(f"正在读取文件: {filename}")
        df = pd.read_csv(filename, sep='\t', names=columns, encoding='utf-8', low_memory=False)
        
        print(f"数据文件: {filename}")
        print(f"总记录数: {len(df):,}")
        print(f"数据列数: {len(df.columns)}")
        print()
        
        # 基本统计信息
        print("1. 数据基本信息")
        print("-" * 40)
        print(f"数据形状: {df.shape}")
        file_size = os.path.getsize(filename) / 1024 / 1024
        print(f"文件大小: {file_size:.2f} MB")
        print()
        
        # 显示前几行数据
        print("2. 数据样本（前3行）")
        print("-" * 40)
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"记录 {i+1}:")
            print(f"  ID: {row['geonameid']}")
            print(f"  名称: {row['name']}")
            print(f"  ASCII名称: {row['asciiname']}")
            print(f"  坐标: ({row['latitude']:.6f}, {row['longitude']:.6f})")
            print(f"  特征: {row['feature_class']}-{row['feature_code']}")
            print(f"  国家: {row['country_code']}")
            print(f"  人口: {row['population'] if pd.notna(row['population']) else 'N/A'}")
            print()
        
        # 特征类别分析
        print("3. 特征类别(Feature Class)分析")
        print("-" * 40)
        feature_class_counts = df['feature_class'].value_counts()
        print("特征类别统计:")
        for fc, count in feature_class_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {fc}: {count:,} 个地点 ({percentage:.1f}%)")
        print()
        
        # 特征代码分析
        print("4. 特征代码(Feature Code)分析")
        print("-" * 40)
        feature_code_counts = df['feature_code'].value_counts().head(15)
        print("前15个最常见的特征代码:")
        for fc, count in feature_code_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {fc}: {count:,} 个地点 ({percentage:.1f}%)")
        print()
        
        # 国家代码分析（对于全球数据）
        if filename == 'allCountries.txt':
            print("5. 国家分布分析")
            print("-" * 40)
            country_counts = df['country_code'].value_counts().head(20)
            print("前20个国家/地区:")
            for country, count in country_counts.items():
                percentage = (count / len(df)) * 100
                print(f"  {country}: {count:,} 个地点 ({percentage:.1f}%)")
            print()
        
        # 行政区分析
        print("5. 行政区分析" if filename != 'allCountries.txt' else "6. 行政区分析")
        print("-" * 40)
        admin1_counts = df['admin1_code'].value_counts().head(20)
        print("前20个一级行政区:")
        for admin, count in admin1_counts.items():
            if pd.notna(admin) and admin != '':
                percentage = (count / len(df)) * 100
                print(f"  {admin}: {count:,} 个地点 ({percentage:.1f}%)")
        print()
        
        # 人口统计
        print("6. 人口统计" if filename != 'allCountries.txt' else "7. 人口统计")
        print("-" * 40)
        population_data = df[df['population'] > 0]['population']
        if len(population_data) > 0:
            print(f"有人口数据的地点: {len(population_data):,} 个 ({len(population_data)/len(df)*100:.1f}%)")
            print(f"总人口: {population_data.sum():,}")
            print(f"平均人口: {population_data.mean():.0f}")
            print(f"人口中位数: {population_data.median():.0f}")
            print(f"最大人口: {population_data.max():,}")
            print(f"最小人口: {population_data.min():,}")
            
            # 人口分布
            print("\n人口规模分布:")
            bins = [0, 1000, 5000, 10000, 50000, 100000, 500000, 1000000, float('inf')]
            labels = ['<1K', '1K-5K', '5K-10K', '10K-50K', '50K-100K', '100K-500K', '500K-1M', '>1M']
            pop_dist = pd.cut(population_data, bins=bins, labels=labels, right=False)
            for label, count in pop_dist.value_counts().sort_index().items():
                print(f"  {label}: {count:,} 个地点")
        else:
            print("没有人口数据")
        print()
        
        # 海拔统计
        print("7. 海拔统计" if filename != 'allCountries.txt' else "8. 海拔统计")
        print("-" * 40)
        elevation_data = df[df['elevation'] != 0]['elevation']
        if len(elevation_data) > 0:
            print(f"有海拔数据的地点: {len(elevation_data):,} 个 ({len(elevation_data)/len(df)*100:.1f}%)")
            print(f"平均海拔: {elevation_data.mean():.1f} 米")
            print(f"海拔中位数: {elevation_data.median():.1f} 米")
            print(f"最高海拔: {elevation_data.max()} 米")
            print(f"最低海拔: {elevation_data.min()} 米")
        else:
            print("没有海拔数据")
        print()
        
        # 地理坐标范围
        print("8. 地理坐标范围" if filename != 'allCountries.txt' else "9. 地理坐标范围")
        print("-" * 40)
        print(f"纬度范围: {df['latitude'].min():.6f} 到 {df['latitude'].max():.6f}")
        print(f"经度范围: {df['longitude'].min():.6f} 到 {df['longitude'].max():.6f}")
        print()
        
        # 时区信息
        print("9. 时区信息" if filename != 'allCountries.txt' else "10. 时区信息")
        print("-" * 40)
        timezone_counts = df['timezone'].value_counts().head(10)
        print("前10个时区:")
        for tz, count in timezone_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {tz}: {count:,} 个地点 ({percentage:.1f}%)")
        print()
        
        # 数据质量检查
        print("10. 数据质量检查" if filename != 'allCountries.txt' else "11. 数据质量检查")
        print("-" * 40)
        print("缺失值统计:")
        missing_data = df.isnull().sum()
        for col, missing in missing_data.items():
            if missing > 0:
                percentage = (missing / len(df)) * 100
                print(f"  {col}: {missing:,} 个缺失值 ({percentage:.1f}%)")
        print()
        
        # 保存处理后的数据
        output_filename = filename.replace('.txt', '_processed.csv')
        df.to_csv(output_filename, index=False, encoding='utf-8-sig')
        print(f"处理后的数据已保存为: {output_filename}")
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def main():
    """主函数"""
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = 'HK.txt'
    
    if not os.path.exists(filename):
        print(f"文件 {filename} 不存在")
        return
    
    # 分析数据
    df = analyze_geonames_data(filename)

if __name__ == "__main__":
    main()
