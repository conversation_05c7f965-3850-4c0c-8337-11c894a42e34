-- =====================================================================================
-- GeoNames全球地理数据库表创建脚本 (Oracle 11g)
-- 创建日期: 2025-08-07
-- 描述: 用于存储GeoNames全球地理位置数据的Oracle数据库表结构
-- 数据来源: https://download.geonames.org/export/dump/
-- 许可证: Creative Commons Attribution 4.0
-- =====================================================================================

-- -- 删除已存在的表（如果存在）
-- BEGIN
--     EXECUTE IMMEDIATE 'DROP TABLE GEONAMES_LOCATIONS CASCADE CONSTRAINTS';
--     DBMS_OUTPUT.PUT_LINE('已删除现有表 GEONAMES_LOCATIONS');
-- EXCEPTION
--     WHEN OTHERS THEN
--         IF SQLCODE != -942 THEN -- 表不存在的错误代码
--             RAISE;
--         END IF;
--         DBMS_OUTPUT.PUT_LINE('表 GEONAMES_LOCATIONS 不存在，继续创建');
-- END;
-- /

-- -- 删除已存在的序列（如果存在）
-- BEGIN
--     EXECUTE IMMEDIATE 'DROP SEQUENCE SEQ_GEONAMES_ID';
--     DBMS_OUTPUT.PUT_LINE('已删除现有序列 SEQ_GEONAMES_ID');
-- EXCEPTION
--     WHEN OTHERS THEN
--         IF SQLCODE != -2289 THEN -- 序列不存在的错误代码
--             RAISE;
--         END IF;
--         DBMS_OUTPUT.PUT_LINE('序列 SEQ_GEONAMES_ID 不存在，继续创建');
-- END;
-- /

-- =====================================================================================
-- 创建主表：GEONAMES_LOCATIONS
-- 描述: 存储全球地理位置的详细信息
-- =====================================================================================

CREATE TABLE GEONAMES_LOCATIONS (
    -- 主键和标识字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID（自增）
    GEONAME_ID          NUMBER(12)      NOT NULL,           -- GeoNames官方ID（来自数据源）
    
    -- 地名信息字段
    NAME                NVARCHAR2(200)  NOT NULL,           -- 地理位置名称（UTF-8，支持多语言）
    ASCII_NAME          VARCHAR2(200),                      -- ASCII格式的地名（英文字符）
    ALTERNATE_NAMES     NCLOB,                              -- 别名列表（逗号分隔，包含多语言名称）
    
    -- 地理坐标字段
    LATITUDE            NUMBER(10,7)    NOT NULL,           -- 纬度（WGS84坐标系，精度到小数点后7位）
    LONGITUDE           NUMBER(10,7)    NOT NULL,           -- 经度（WGS84坐标系，精度到小数点后7位）
    
    -- 地理特征分类字段
    FEATURE_CLASS       CHAR(1)         NOT NULL,           -- 特征类别（A/H/L/P/R/S/T/U/V）
    FEATURE_CODE        VARCHAR2(10)    NOT NULL,           -- 特征代码（详细分类，如PPL/MT/HTL等）
    
    -- 行政区划字段
    COUNTRY_CODE        CHAR(2)         NOT NULL,           -- 国家代码（ISO-3166 2位字母代码）
    CC2                 VARCHAR2(200),                      -- 备用国家代码（逗号分隔）
    ADMIN1_CODE         VARCHAR2(20),                       -- 一级行政区代码（省/州级）
    ADMIN2_CODE         VARCHAR2(80),                       -- 二级行政区代码（市/县级）
    ADMIN3_CODE         VARCHAR2(20),                       -- 三级行政区代码（区/镇级）
    ADMIN4_CODE         VARCHAR2(20),                       -- 四级行政区代码（街道/村级）
    
    -- 人口和海拔信息
    POPULATION          NUMBER(12),                         -- 人口数量（可为空）
    ELEVATION           NUMBER(6),                          -- 海拔高度（米，可为负值）
    DEM                 NUMBER(6),                          -- 数字高程模型（SRTM3或GTOPO30）
    
    -- 时区和时间信息
    TIMEZONE            VARCHAR2(40),                       -- IANA时区标识符（如Asia/Shanghai）
    MODIFICATION_DATE   DATE,                               -- 最后修改日期
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    DATA_SOURCE         VARCHAR2(50)    DEFAULT 'GEONAMES', -- 数据来源标识
    
    -- 主键约束
    CONSTRAINT PK_GEONAMES_LOCATIONS PRIMARY KEY (ID),
    
    -- 唯一约束
    CONSTRAINT UK_GEONAMES_ID UNIQUE (GEONAME_ID),
    
    -- 检查约束
    CONSTRAINT CK_LATITUDE CHECK (LATITUDE BETWEEN -90 AND 90),
    CONSTRAINT CK_LONGITUDE CHECK (LONGITUDE BETWEEN -180 AND 180),
    CONSTRAINT CK_FEATURE_CLASS CHECK (FEATURE_CLASS IN ('A','H','L','P','R','S','T','U','V')),
    CONSTRAINT CK_POPULATION CHECK (POPULATION >= 0),
    CONSTRAINT CK_COUNTRY_CODE CHECK (LENGTH(COUNTRY_CODE) = 2)
);

-- =====================================================================================
-- 添加表注释
-- =====================================================================================

COMMENT ON TABLE GEONAMES_LOCATIONS IS '全球地理位置数据表 - 存储来自GeoNames数据库的全球地理位置信息，包括城市、山峰、河流、建筑物等各类地理实体的详细信息';

-- 添加字段注释
COMMENT ON COLUMN GEONAMES_LOCATIONS.ID IS '内部主键ID - 数据库自动生成的唯一标识符';
COMMENT ON COLUMN GEONAMES_LOCATIONS.GEONAME_ID IS 'GeoNames官方ID - 来自GeoNames数据库的原始唯一标识符';
COMMENT ON COLUMN GEONAMES_LOCATIONS.NAME IS '地理位置名称 - UTF-8编码的地名，支持中文、日文等多语言字符';
COMMENT ON COLUMN GEONAMES_LOCATIONS.ASCII_NAME IS 'ASCII地名 - 纯英文字符的地名，便于系统处理和搜索';
COMMENT ON COLUMN GEONAMES_LOCATIONS.ALTERNATE_NAMES IS '别名列表 - 逗号分隔的多语言别名，包括历史名称、简称、当地语言名称等';
COMMENT ON COLUMN GEONAMES_LOCATIONS.LATITUDE IS '纬度 - WGS84坐标系纬度，范围-90到90度，精度到小数点后7位';
COMMENT ON COLUMN GEONAMES_LOCATIONS.LONGITUDE IS '经度 - WGS84坐标系经度，范围-180到180度，精度到小数点后7位';
COMMENT ON COLUMN GEONAMES_LOCATIONS.FEATURE_CLASS IS '特征类别 - A:行政区 H:水文 L:区域 P:居住地 R:道路 S:建筑 T:地形 U:海底 V:植被';
COMMENT ON COLUMN GEONAMES_LOCATIONS.FEATURE_CODE IS '特征代码 - 详细分类代码，如PPL:居住地 MT:山峰 HTL:酒店 STM:河流等';
COMMENT ON COLUMN GEONAMES_LOCATIONS.COUNTRY_CODE IS '国家代码 - ISO-3166标准2位字母国家代码，如CN:中国 JP:日本 US:美国';
COMMENT ON COLUMN GEONAMES_LOCATIONS.CC2 IS '备用国家代码 - 逗号分隔的备用国家代码列表';
COMMENT ON COLUMN GEONAMES_LOCATIONS.ADMIN1_CODE IS '一级行政区代码 - 省/州级行政区代码，对应admin1CodesASCII.txt文件';
COMMENT ON COLUMN GEONAMES_LOCATIONS.ADMIN2_CODE IS '二级行政区代码 - 市/县级行政区代码，对应admin2Codes.txt文件';
COMMENT ON COLUMN GEONAMES_LOCATIONS.ADMIN3_CODE IS '三级行政区代码 - 区/镇级行政区代码';
COMMENT ON COLUMN GEONAMES_LOCATIONS.ADMIN4_CODE IS '四级行政区代码 - 街道/村级行政区代码';
COMMENT ON COLUMN GEONAMES_LOCATIONS.POPULATION IS '人口数量 - 该地区的人口数量，仅对有人口统计的地区有值';
COMMENT ON COLUMN GEONAMES_LOCATIONS.ELEVATION IS '海拔高度 - 以米为单位的海拔高度，可为负值（如死海）';
COMMENT ON COLUMN GEONAMES_LOCATIONS.DEM IS '数字高程模型 - SRTM3(90m精度)或GTOPO30(900m精度)的平均海拔';
COMMENT ON COLUMN GEONAMES_LOCATIONS.TIMEZONE IS '时区标识符 - IANA标准时区ID，如Asia/Shanghai Asia/Tokyo等';
COMMENT ON COLUMN GEONAMES_LOCATIONS.MODIFICATION_DATE IS '最后修改日期 - GeoNames数据库中该记录的最后修改时间';
COMMENT ON COLUMN GEONAMES_LOCATIONS.CREATED_DATE IS '记录创建时间 - 该记录在本数据库中的创建时间';
COMMENT ON COLUMN GEONAMES_LOCATIONS.UPDATED_DATE IS '记录更新时间 - 该记录在本数据库中的最后更新时间';
COMMENT ON COLUMN GEONAMES_LOCATIONS.DATA_SOURCE IS '数据来源标识 - 标识数据来源，默认为GEONAMES';

-- =====================================================================================
-- 创建序列用于主键自增
-- =====================================================================================

CREATE SEQUENCE SEQ_GEONAMES_ID
    START WITH 1
    INCREMENT BY 1
    NOMAXVALUE
    NOCYCLE
    CACHE 1000;

COMMENT ON SEQUENCE SEQ_GEONAMES_ID IS '地理位置表主键序列 - 用于生成GEONAMES_LOCATIONS表的主键ID';

-- =====================================================================================
-- 创建触发器实现主键自增和更新时间自动维护
-- =====================================================================================

CREATE OR REPLACE TRIGGER TRG_GEONAMES_LOCATIONS_BI
    BEFORE INSERT ON GEONAMES_LOCATIONS
    FOR EACH ROW
BEGIN
    -- 自动生成主键ID
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_ID.NEXTVAL;
    END IF;
    
    -- 设置创建时间和更新时间
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

CREATE OR REPLACE TRIGGER TRG_GEONAMES_LOCATIONS_BU
    BEFORE UPDATE ON GEONAMES_LOCATIONS
    FOR EACH ROW
BEGIN
    -- 更新修改时间
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- =====================================================================================
-- 创建索引以提高查询性能
-- =====================================================================================

-- 地理坐标索引（用于空间查询）
CREATE INDEX IDX_GEONAMES_COORDINATES ON GEONAMES_LOCATIONS (LATITUDE, LONGITUDE);

-- 国家代码索引（用于按国家查询）
CREATE INDEX IDX_GEONAMES_COUNTRY ON GEONAMES_LOCATIONS (COUNTRY_CODE);

-- 特征类别和代码索引（用于按类型查询）
CREATE INDEX IDX_GEONAMES_FEATURE ON GEONAMES_LOCATIONS (FEATURE_CLASS, FEATURE_CODE);

-- 地名索引（用于名称搜索）
CREATE INDEX IDX_GEONAMES_NAME ON GEONAMES_LOCATIONS (NAME);
CREATE INDEX IDX_GEONAMES_ASCII_NAME ON GEONAMES_LOCATIONS (ASCII_NAME);

-- 行政区索引（用于按行政区查询）
CREATE INDEX IDX_GEONAMES_ADMIN1 ON GEONAMES_LOCATIONS (COUNTRY_CODE, ADMIN1_CODE);
CREATE INDEX IDX_GEONAMES_ADMIN2 ON GEONAMES_LOCATIONS (COUNTRY_CODE, ADMIN1_CODE, ADMIN2_CODE);

-- 人口索引（用于按人口规模查询）
CREATE INDEX IDX_GEONAMES_POPULATION ON GEONAMES_LOCATIONS (POPULATION);

-- 时区索引（用于按时区查询）
CREATE INDEX IDX_GEONAMES_TIMEZONE ON GEONAMES_LOCATIONS (TIMEZONE);

-- 修改日期索引（用于增量更新）
CREATE INDEX IDX_GEONAMES_MOD_DATE ON GEONAMES_LOCATIONS (MODIFICATION_DATE);

-- =====================================================================================
-- 添加索引注释
-- =====================================================================================

COMMENT ON INDEX IDX_GEONAMES_COORDINATES IS '地理坐标复合索引 - 用于基于经纬度的空间查询和范围搜索';
COMMENT ON INDEX IDX_GEONAMES_COUNTRY IS '国家代码索引 - 用于按国家/地区快速筛选数据';
COMMENT ON INDEX IDX_GEONAMES_FEATURE IS '特征分类复合索引 - 用于按地理特征类型和代码查询';
COMMENT ON INDEX IDX_GEONAMES_NAME IS '地名索引 - 用于按地名进行模糊搜索和精确匹配';
COMMENT ON INDEX IDX_GEONAMES_ASCII_NAME IS 'ASCII地名索引 - 用于英文地名搜索';
COMMENT ON INDEX IDX_GEONAMES_ADMIN1 IS '一级行政区复合索引 - 用于按国家和省/州查询';
COMMENT ON INDEX IDX_GEONAMES_ADMIN2 IS '二级行政区复合索引 - 用于按国家、省/州、市/县查询';
COMMENT ON INDEX IDX_GEONAMES_POPULATION IS '人口索引 - 用于按人口规模排序和筛选';
COMMENT ON INDEX IDX_GEONAMES_TIMEZONE IS '时区索引 - 用于按时区分组和查询';
COMMENT ON INDEX IDX_GEONAMES_MOD_DATE IS '修改日期索引 - 用于增量数据更新和同步';

-- -- =====================================================================================
-- -- 创建视图用于常用查询
-- -- =====================================================================================

-- -- 城市视图（人口大于1000的居住地）
-- CREATE OR REPLACE VIEW V_GEONAMES_CITIES AS
-- SELECT 
--     ID,
--     GEONAME_ID,
--     NAME,
--     ASCII_NAME,
--     LATITUDE,
--     LONGITUDE,
--     COUNTRY_CODE,
--     ADMIN1_CODE,
--     ADMIN2_CODE,
--     POPULATION,
--     ELEVATION,
--     TIMEZONE,
--     MODIFICATION_DATE
-- FROM GEONAMES_LOCATIONS
-- WHERE FEATURE_CLASS = 'P' 
--   AND FEATURE_CODE IN ('PPL', 'PPLA', 'PPLA2', 'PPLA3', 'PPLA4', 'PPLC')
--   AND (POPULATION IS NULL OR POPULATION >= 1000);

-- COMMENT ON VIEW V_GEONAMES_CITIES IS '城市视图 - 显示人口大于1000的居住地，包括城市、镇、行政中心等';

-- -- 地标建筑视图
-- CREATE OR REPLACE VIEW V_GEONAMES_LANDMARKS AS
-- SELECT 
--     ID,
--     GEONAME_ID,
--     NAME,
--     ASCII_NAME,
--     LATITUDE,
--     LONGITUDE,
--     FEATURE_CLASS,
--     FEATURE_CODE,
--     COUNTRY_CODE,
--     ADMIN1_CODE,
--     TIMEZONE
-- FROM GEONAMES_LOCATIONS
-- WHERE FEATURE_CODE IN ('HTL', 'MTRO', 'AIRP', 'TMPL', 'UNIV', 'HOSP', 'MUS');

-- COMMENT ON VIEW V_GEONAMES_LANDMARKS IS '地标建筑视图 - 显示酒店、地铁站、机场、寺庙、大学、医院、博物馆等重要建筑';

-- -- =====================================================================================
-- -- 输出创建完成信息
-- -- =====================================================================================

-- BEGIN
--     DBMS_OUTPUT.PUT_LINE('=====================================================================================');
--     DBMS_OUTPUT.PUT_LINE('GeoNames数据库表创建完成！');
--     DBMS_OUTPUT.PUT_LINE('=====================================================================================');
--     DBMS_OUTPUT.PUT_LINE('已创建的对象:');
--     DBMS_OUTPUT.PUT_LINE('1. 表: GEONAMES_LOCATIONS (主表)');
--     DBMS_OUTPUT.PUT_LINE('2. 序列: SEQ_GEONAMES_ID (主键序列)');
--     DBMS_OUTPUT.PUT_LINE('3. 触发器: TRG_GEONAMES_LOCATIONS_BI, TRG_GEONAMES_LOCATIONS_BU');
--     DBMS_OUTPUT.PUT_LINE('4. 索引: 9个性能优化索引');
--     DBMS_OUTPUT.PUT_LINE('5. 视图: V_GEONAMES_CITIES, V_GEONAMES_LANDMARKS');
--     DBMS_OUTPUT.PUT_LINE('');
--     DBMS_OUTPUT.PUT_LINE('数据导入建议:');
--     DBMS_OUTPUT.PUT_LINE('- 使用SQL*Loader或外部表进行批量数据导入');
--     DBMS_OUTPUT.PUT_LINE('- 导入前可临时禁用索引以提高性能');
--     DBMS_OUTPUT.PUT_LINE('- 建议按国家分批导入大量数据');
--     DBMS_OUTPUT.PUT_LINE('=====================================================================================');
-- END;
-- /
