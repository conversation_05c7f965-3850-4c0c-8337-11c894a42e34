#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeoNames数据入库程序
支持将GeoNames数据导入Oracle数据库的标准表和业务友好表
"""

import cx_Oracle
import pandas as pd
import numpy as np
from datetime import datetime
import os
import json
from collections import defaultdict

class GeoNamesDataLoader:
    """GeoNames数据加载器"""
    
    def __init__(self, connection_string):
        """
        初始化数据加载器
        connection_string: Oracle连接字符串，如 "username/password@localhost:1521/xe"
        """
        self.connection_string = connection_string
        self.connection = None
        self.cursor = None
        
        # 辅助数据
        self.feature_codes = {}
        self.admin_codes = {}
        self.country_info = {}
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = cx_Oracle.connect(self.connection_string)
            self.cursor = self.connection.cursor()
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("✅ 数据库连接已关闭")
    
    def load_auxiliary_data(self):
        """加载辅助数据"""
        print("🔄 加载辅助数据...")
        
        # 1. 加载特征代码
        try:
            with open('featureCodes_en.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        self.feature_codes[parts[0]] = {
                            'name': parts[1] if len(parts) > 1 else '',
                            'description': parts[2] if len(parts) > 2 else ''
                        }
            print(f"✅ 特征代码: {len(self.feature_codes)} 个")
        except Exception as e:
            print(f"⚠️ 特征代码加载失败: {e}")
        
        # 2. 加载行政区代码
        try:
            with open('admin1CodesASCII.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        self.admin_codes[parts[0]] = parts[1]
            print(f"✅ 行政区代码: {len(self.admin_codes)} 个")
        except Exception as e:
            print(f"⚠️ 行政区代码加载失败: {e}")
        
        # 3. 加载国家信息
        try:
            with open('countryInfo.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.startswith('#') and line.strip():
                        parts = line.strip().split('\t')
                        if len(parts) >= 16:
                            self.country_info[parts[0]] = {
                                'name': parts[4],
                                'continent': parts[8],
                                'currency_code': parts[10],
                                'currency_name': parts[11],
                                'languages': parts[15],
                                'capital': parts[5]
                            }
            print(f"✅ 国家信息: {len(self.country_info)} 个")
        except Exception as e:
            print(f"⚠️ 国家信息加载失败: {e}")
    
    def get_country_name_cn(self, country_code):
        """获取国家中文名称"""
        cn_names = {
            'CN': '中国', 'HK': '香港', 'TW': '台湾', 'MO': '澳门',
            'JP': '日本', 'KR': '韩国', 'US': '美国', 'GB': '英国',
            'FR': '法国', 'DE': '德国', 'IT': '意大利', 'ES': '西班牙',
            'CA': '加拿大', 'AU': '澳大利亚', 'BR': '巴西', 'IN': '印度',
            'RU': '俄罗斯', 'MX': '墨西哥', 'TH': '泰国', 'SG': '新加坡'
        }
        return cn_names.get(str(country_code), '')
    
    def get_continent_name(self, continent_code):
        """获取大洲中文名称"""
        continent_map = {
            'AF': '非洲', 'AS': '亚洲', 'EU': '欧洲', 'NA': '北美洲',
            'OC': '大洋洲', 'SA': '南美洲', 'AN': '南极洲'
        }
        return continent_map.get(str(continent_code), '')
    
    def get_feature_name_cn(self, feature_class, feature_code):
        """获取特征中文名称"""
        feature_cn_map = {
            'P.PPL': '居住地', 'P.PPLA': '一级行政中心', 'P.PPLA2': '二级行政中心',
            'P.PPLC': '首都', 'S.HTL': '酒店', 'S.AIRP': '机场',
            'S.MTRO': '地铁站', 'T.MT': '山峰', 'T.HLL': '丘陵',
            'H.BAY': '海湾', 'H.LK': '湖泊', 'T.ISL': '岛屿'
        }
        full_code = f"{feature_class}.{feature_code}"
        return feature_cn_map.get(full_code, feature_code)
    
    def get_feature_category(self, feature_class):
        """获取特征大类别"""
        category_map = {
            'A': '行政区域', 'H': '水文地理', 'L': '区域地带', 'P': '居住地点',
            'R': '交通设施', 'S': '建筑景点', 'T': '地形地貌', 'U': '海底地形', 'V': '植被覆盖'
        }
        return category_map.get(str(feature_class), '其他')
    
    def get_business_category(self, feature_class, feature_code):
        """获取业务分类"""
        if feature_class == 'P':
            if feature_code == 'PPLC':
                return '首都城市'
            elif feature_code in ['PPLA', 'PPLA2']:
                return '重要城市'
            else:
                return '一般城市'
        elif feature_class == 'S':
            if feature_code == 'HTL':
                return '酒店住宿'
            elif feature_code == 'AIRP':
                return '交通枢纽'
            else:
                return '服务设施'
        elif feature_class == 'T':
            return '自然景观'
        elif feature_class == 'H':
            return '水域地理'
        else:
            return '其他地点'
    
    def calculate_importance_score(self, feature_code, population):
        """计算重要性评分"""
        score = 20  # 基础分
        
        # 基于特征代码的评分
        if feature_code == 'PPLC':
            score = 100  # 首都
        elif feature_code in ['PPLA', 'PPLA2']:
            score = 80   # 行政中心
        elif feature_code == 'AIRP':
            score = 70   # 机场
        
        # 基于人口的评分加成
        try:
            pop = int(float(population)) if population and str(population) != 'nan' else 0
            if pop > 1000000:
                score += 30
            elif pop > 100000:
                score += 20
            elif pop > 10000:
                score += 10
        except:
            pass
        
        return min(score, 100)
    
    def format_population(self, population):
        """格式化人口数字"""
        try:
            pop = int(float(population)) if population and str(population) != 'nan' else 0
            if pop == 0:
                return ''
            elif pop >= 1000000:
                return f"{pop/1000000:.1f}百万"
            elif pop >= 10000:
                return f"{pop/10000:.1f}万"
            else:
                return f"{pop:,}"
        except:
            return ''
    
    def safe_str(self, value, max_length=None):
        """安全转换为字符串"""
        if pd.isna(value) or value is None:
            return None
        result = str(value)
        if max_length and len(result) > max_length:
            result = result[:max_length]
        return result
    
    def safe_number(self, value):
        """安全转换为数字"""
        if pd.isna(value) or value is None:
            return None
        try:
            return float(value)
        except:
            return None
    
    def load_standard_data(self, data_file, batch_size=1000):
        """加载标准数据到GEONAMES_STANDARD表"""
        print(f"🔄 开始加载标准数据: {data_file}")
        
        # 定义列名
        columns = [
            'geonameid', 'name', 'asciiname', 'alternatenames',
            'latitude', 'longitude', 'feature_class', 'feature_code',
            'country_code', 'cc2', 'admin1_code', 'admin2_code',
            'admin3_code', 'admin4_code', 'population', 'elevation',
            'dem', 'timezone', 'modification_date'
        ]
        
        # 准备SQL语句
        insert_sql = """
        INSERT INTO GEONAMES_STANDARD (
            GEONAME_ID, NAME, ASCII_NAME, ALTERNATE_NAMES,
            LATITUDE, LONGITUDE, FEATURE_CLASS, FEATURE_CODE,
            COUNTRY_CODE, CC2, ADMIN1_CODE, ADMIN2_CODE,
            ADMIN3_CODE, ADMIN4_CODE, POPULATION, ELEVATION,
            DEM, TIMEZONE, MODIFICATION_DATE
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10,
            :11, :12, :13, :14, :15, :16, :17, :18, :19
        )
        """
        
        total_processed = 0
        error_count = 0
        
        try:
            # 分块读取文件
            for chunk in pd.read_csv(data_file, sep='\t', names=columns, 
                                   encoding='utf-8', chunksize=batch_size, low_memory=False):
                
                batch_data = []
                for _, row in chunk.iterrows():
                    try:
                        # 处理日期
                        mod_date = None
                        if pd.notna(row['modification_date']):
                            try:
                                mod_date = datetime.strptime(str(row['modification_date']), '%Y-%m-%d')
                            except:
                                mod_date = None
                        
                        # 准备数据
                        record = (
                            self.safe_number(row['geonameid']),
                            self.safe_str(row['name'], 200),
                            self.safe_str(row['asciiname'], 200),
                            self.safe_str(row['alternatenames']),
                            self.safe_number(row['latitude']),
                            self.safe_number(row['longitude']),
                            self.safe_str(row['feature_class'], 1),
                            self.safe_str(row['feature_code'], 10),
                            self.safe_str(row['country_code'], 2),
                            self.safe_str(row['cc2'], 200),
                            self.safe_str(row['admin1_code'], 20),
                            self.safe_str(row['admin2_code'], 80),
                            self.safe_str(row['admin3_code'], 20),
                            self.safe_str(row['admin4_code'], 20),
                            self.safe_number(row['population']),
                            self.safe_number(row['elevation']),
                            self.safe_number(row['dem']),
                            self.safe_str(row['timezone'], 40),
                            mod_date
                        )
                        batch_data.append(record)
                        
                    except Exception as e:
                        error_count += 1
                        if error_count <= 5:
                            print(f"⚠️ 处理记录出错: {e}")
                        continue
                
                # 批量插入
                if batch_data:
                    try:
                        self.cursor.executemany(insert_sql, batch_data)
                        self.connection.commit()
                        total_processed += len(batch_data)
                        print(f"✅ 已处理 {total_processed:,} 条标准数据记录")
                    except Exception as e:
                        print(f"❌ 批量插入失败: {e}")
                        self.connection.rollback()
        
        except Exception as e:
            print(f"❌ 加载标准数据失败: {e}")
            return False
        
        print(f"🎉 标准数据加载完成: 成功 {total_processed:,} 条，错误 {error_count} 条")
        return True
    
    def load_business_data(self, data_file, batch_size=1000):
        """加载业务友好数据到GEONAMES_BUSINESS_FRIENDLY表"""
        print(f"🔄 开始加载业务友好数据: {data_file}")
        
        # 定义列名
        columns = [
            'geonameid', 'name', 'asciiname', 'alternatenames',
            'latitude', 'longitude', 'feature_class', 'feature_code',
            'country_code', 'cc2', 'admin1_code', 'admin2_code',
            'admin3_code', 'admin4_code', 'population', 'elevation',
            'dem', 'timezone', 'modification_date'
        ]
        
        # 准备SQL语句
        insert_sql = """
        INSERT INTO GEONAMES_BUSINESS_FRIENDLY (
            GEONAME_ID, NAME, ASCII_NAME, ALTERNATE_NAMES,
            LATITUDE, LONGITUDE, FEATURE_CLASS, FEATURE_CODE,
            FEATURE_NAME, FEATURE_NAME_CN, FEATURE_CATEGORY, FEATURE_DESCRIPTION,
            COUNTRY_CODE, COUNTRY_NAME, COUNTRY_NAME_CN, CONTINENT_CODE, CONTINENT_NAME,
            CAPITAL, CURRENCY_CODE, CURRENCY_NAME, LANGUAGES,
            ADMIN1_CODE, ADMIN1_NAME, ADMIN2_CODE, ADMIN_FULL_PATH,
            POPULATION, POPULATION_FORMATTED, POPULATION_LEVEL,
            ELEVATION, ELEVATION_FORMATTED, ELEVATION_LEVEL,
            TIMEZONE, GMT_OFFSET, TIMEZONE_FORMATTED,
            BUSINESS_CATEGORY, IMPORTANCE_SCORE,
            IS_MAJOR_CITY, IS_CAPITAL, IS_TOURIST_ATTRACTION, IS_TRANSPORT_HUB,
            CC2, ADMIN3_CODE, ADMIN4_CODE, DEM, MODIFICATION_DATE
        ) VALUES (
            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10,
            :11, :12, :13, :14, :15, :16, :17, :18, :19, :20,
            :21, :22, :23, :24, :25, :26, :27, :28, :29, :30,
            :31, :32, :33, :34, :35, :36, :37, :38, :39, :40,
            :41, :42, :43, :44, :45
        )
        """
        
        total_processed = 0
        error_count = 0
        
        try:
            # 分块读取文件
            for chunk in pd.read_csv(data_file, sep='\t', names=columns, 
                                   encoding='utf-8', chunksize=batch_size, low_memory=False):
                
                batch_data = []
                for _, row in chunk.iterrows():
                    try:
                        # 获取辅助信息
                        country_info = self.country_info.get(str(row['country_code']), {})
                        feature_info = self.feature_codes.get(f"{row['feature_class']}.{row['feature_code']}", {})
                        admin1_name = self.admin_codes.get(f"{row['country_code']}.{row['admin1_code']}", '')
                        
                        # 处理日期
                        mod_date = None
                        if pd.notna(row['modification_date']):
                            try:
                                mod_date = datetime.strptime(str(row['modification_date']), '%Y-%m-%d')
                            except:
                                mod_date = None
                        
                        # 计算业务字段
                        population = self.safe_number(row['population'])
                        importance_score = self.calculate_importance_score(row['feature_code'], population)
                        is_major_city = 'Y' if (row['feature_code'] in ['PPLC', 'PPLA', 'PPLA2'] or (population and population > 100000)) else 'N'
                        is_capital = 'Y' if row['feature_code'] == 'PPLC' else 'N'
                        is_tourist = 'Y' if row['feature_code'] in ['HTL', 'MUS', 'TMPL', 'AIRP'] else 'N'
                        is_transport = 'Y' if row['feature_code'] in ['AIRP', 'MTRO'] else 'N'
                        
                        # 人口等级
                        pop_level = ''
                        if population:
                            if population > 1000000:
                                pop_level = '大城市'
                            elif population > 100000:
                                pop_level = '中等城市'
                            elif population > 10000:
                                pop_level = '小城市'
                        
                        # 海拔等级
                        elevation = self.safe_number(row['elevation'])
                        elev_level = ''
                        if elevation:
                            if elevation > 3000:
                                elev_level = '高原'
                            elif elevation > 1000:
                                elev_level = '山地'
                            else:
                                elev_level = '平原'
                        
                        # 构建完整路径
                        admin_path = country_info.get('name', str(row['country_code']))
                        if admin1_name:
                            admin_path += f" > {admin1_name}"
                        
                        # 准备数据
                        record = (
                            self.safe_number(row['geonameid']),                    # 1
                            self.safe_str(row['name'], 200),                       # 2
                            self.safe_str(row['asciiname'], 200),                  # 3
                            self.safe_str(row['alternatenames']),                  # 4
                            self.safe_number(row['latitude']),                     # 5
                            self.safe_number(row['longitude']),                    # 6
                            self.safe_str(row['feature_class'], 1),                # 7
                            self.safe_str(row['feature_code'], 10),                # 8
                            feature_info.get('name', ''),                          # 9
                            self.get_feature_name_cn(row['feature_class'], row['feature_code']), # 10
                            self.get_feature_category(row['feature_class']),       # 11
                            feature_info.get('description', ''),                   # 12
                            self.safe_str(row['country_code'], 2),                 # 13
                            country_info.get('name', ''),                          # 14
                            self.get_country_name_cn(row['country_code']),         # 15
                            country_info.get('continent', ''),                     # 16
                            self.get_continent_name(country_info.get('continent', '')), # 17
                            country_info.get('capital', ''),                       # 18
                            country_info.get('currency_code', ''),                 # 19
                            country_info.get('currency_name', ''),                 # 20
                            country_info.get('languages', ''),                     # 21
                            self.safe_str(row['admin1_code'], 20),                 # 22
                            admin1_name,                                            # 23
                            self.safe_str(row['admin2_code'], 80),                 # 24
                            admin_path,                                             # 25
                            population,                                             # 26
                            self.format_population(population),                    # 27
                            pop_level,                                              # 28
                            elevation,                                              # 29
                            f"{int(elevation)}米" if elevation else '',            # 30
                            elev_level,                                             # 31
                            self.safe_str(row['timezone'], 40),                   # 32
                            0.0,  # GMT_OFFSET 简化处理                            # 33
                            'GMT',  # TIMEZONE_FORMATTED 简化处理                  # 34
                            self.get_business_category(row['feature_class'], row['feature_code']), # 35
                            importance_score,                                       # 36
                            is_major_city,                                          # 37
                            is_capital,                                             # 38
                            is_tourist,                                             # 39
                            is_transport,                                           # 40
                            self.safe_str(row['cc2'], 200),                        # 41
                            self.safe_str(row['admin3_code'], 20),                 # 42
                            self.safe_str(row['admin4_code'], 20),                 # 43
                            self.safe_number(row['dem']),                          # 44
                            mod_date                                                # 45
                        )
                        batch_data.append(record)
                        
                    except Exception as e:
                        error_count += 1
                        if error_count <= 5:
                            print(f"⚠️ 处理记录出错: {e}")
                        continue
                
                # 批量插入
                if batch_data:
                    try:
                        self.cursor.executemany(insert_sql, batch_data)
                        self.connection.commit()
                        total_processed += len(batch_data)
                        print(f"✅ 已处理 {total_processed:,} 条业务友好数据记录")
                    except Exception as e:
                        print(f"❌ 批量插入失败: {e}")
                        self.connection.rollback()
        
        except Exception as e:
            print(f"❌ 加载业务友好数据失败: {e}")
            return False
        
        print(f"🎉 业务友好数据加载完成: 成功 {total_processed:,} 条，错误 {error_count} 条")
        return True
    
    def load_data(self, data_file, load_standard=True, load_business=True, batch_size=1000):
        """加载数据到两个表"""
        print("🚀 开始GeoNames数据加载")
        print("=" * 60)
        
        # 检查文件是否存在
        if not os.path.exists(data_file):
            print(f"❌ 数据文件不存在: {data_file}")
            return False
        
        # 连接数据库
        if not self.connect():
            return False
        
        # 加载辅助数据
        self.load_auxiliary_data()
        
        success = True
        
        try:
            # 加载标准数据
            if load_standard:
                print(f"\n📊 加载标准数据...")
                if not self.load_standard_data(data_file, batch_size):
                    success = False
            
            # 加载业务友好数据
            if load_business:
                print(f"\n🎯 加载业务友好数据...")
                if not self.load_business_data(data_file, batch_size):
                    success = False
            
        finally:
            self.disconnect()
        
        if success:
            print("\n🎉 数据加载完成！")
            print("可以使用以下SQL查看对比效果:")
            print("  SELECT * FROM V_DATA_COMPARISON;")
            print("  SELECT * FROM V_BUSINESS_VALUE_DEMO;")
        
        return success

def main():
    """主函数"""
    print("GeoNames数据入库程序")
    print("=" * 50)
    
    # 配置数据库连接
    connection_string = "manifest_dcb/manifest_dcb@192.168.1.151/TEST"
    if not connection_string:
        print("❌ 连接字符串不能为空")
        return
    
    # 选择数据文件
    print("\n可用的数据文件:")
    data_files = []
    for file in os.listdir('.'):
        if file.endswith('.txt') and file in ['HK.txt', 'CN.txt', 'JP.txt', 'allCountries.txt']:
            data_files.append(file)
            print(f"  {len(data_files)}. {file}")
    
    if not data_files:
        print("❌ 未找到可用的数据文件")
        return
    
    try:
        choice = int(input(f"\n请选择数据文件 (1-{len(data_files)}): ")) - 1
        if choice < 0 or choice >= len(data_files):
            print("❌ 无效选择")
            return
        
        data_file = data_files[choice]
        print(f"✅ 选择数据文件: {data_file}")
        
        # 选择加载选项
        print("\n加载选项:")
        print("  1. 仅加载标准表")
        print("  2. 仅加载业务友好表")
        print("  3. 加载两个表（推荐）")
        
        load_choice = input("请选择 (1-3): ").strip()
        
        load_standard = load_choice in ['1', '3']
        load_business = load_choice in ['2', '3']
        
        # 批次大小
        batch_size = int(input("请输入批次大小 (建议1000): ") or "1000")
        
        # 创建加载器并执行
        loader = GeoNamesDataLoader(connection_string)
        loader.load_data(data_file, load_standard, load_business, batch_size)
        
    except ValueError:
        print("❌ 输入无效")
    except KeyboardInterrupt:
        print("\n❌ 用户取消操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
