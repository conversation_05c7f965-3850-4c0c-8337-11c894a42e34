#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版业务友好数据生成器
解决编码问题，生成核心业务友好字段
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def load_feature_codes():
    """加载特征代码"""
    feature_codes = {}
    try:
        with open('featureCodes_en.txt', 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    feature_codes[parts[0]] = parts[1]
        print(f"✅ 加载特征代码: {len(feature_codes)} 个")
    except Exception as e:
        print(f"⚠️ 特征代码加载失败: {e}")
    return feature_codes

def load_admin_codes():
    """加载行政区代码"""
    admin_codes = {}
    try:
        with open('admin1CodesASCII.txt', 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    admin_codes[parts[0]] = parts[1]
        print(f"✅ 加载行政区代码: {len(admin_codes)} 个")
    except Exception as e:
        print(f"⚠️ 行政区代码加载失败: {e}")
    return admin_codes

def load_country_info():
    """加载国家信息"""
    country_info = {}
    try:
        with open('countryInfo.txt', 'r', encoding='utf-8') as f:
            for line in f:
                if not line.startswith('#') and line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 16:
                        country_info[parts[0]] = {
                            'name': parts[4],
                            'continent': parts[8],
                            'currency': parts[11]
                        }
        print(f"✅ 加载国家信息: {len(country_info)} 个")
    except Exception as e:
        print(f"⚠️ 国家信息加载失败: {e}")
    return country_info

def get_country_name_cn(country_code):
    """获取国家中文名称"""
    cn_names = {
        'CN': '中国', 'HK': '香港', 'TW': '台湾', 'MO': '澳门',
        'JP': '日本', 'KR': '韩国', 'US': '美国', 'GB': '英国',
        'FR': '法国', 'DE': '德国', 'IT': '意大利', 'ES': '西班牙',
        'CA': '加拿大', 'AU': '澳大利亚', 'BR': '巴西', 'IN': '印度',
        'RU': '俄罗斯', 'MX': '墨西哥', 'TH': '泰国', 'SG': '新加坡'
    }
    return cn_names.get(str(country_code), '')

def get_continent_name(continent_code):
    """获取大洲中文名称"""
    continent_map = {
        'AF': '非洲', 'AS': '亚洲', 'EU': '欧洲', 'NA': '北美洲',
        'OC': '大洋洲', 'SA': '南美洲', 'AN': '南极洲'
    }
    return continent_map.get(str(continent_code), '')

def get_feature_category(feature_class):
    """获取特征大类别"""
    category_map = {
        'A': '行政区域', 'H': '水文地理', 'L': '区域地带', 'P': '居住地点',
        'R': '交通设施', 'S': '建筑景点', 'T': '地形地貌', 'U': '海底地形', 'V': '植被覆盖'
    }
    return category_map.get(str(feature_class), '其他')

def get_business_category(feature_class, feature_code):
    """获取业务分类"""
    if feature_class == 'P':
        if feature_code == 'PPLC':
            return '首都城市'
        elif feature_code in ['PPLA', 'PPLA2']:
            return '重要城市'
        else:
            return '一般城市'
    elif feature_class == 'S':
        if feature_code == 'HTL':
            return '酒店住宿'
        elif feature_code == 'AIRP':
            return '交通枢纽'
        else:
            return '服务设施'
    elif feature_class == 'T':
        return '自然景观'
    elif feature_class == 'H':
        return '水域地理'
    else:
        return '其他地点'

def calculate_importance_score(feature_code, population):
    """计算重要性评分"""
    score = 20  # 基础分
    
    # 基于特征代码的评分
    if feature_code == 'PPLC':
        score = 100  # 首都
    elif feature_code in ['PPLA', 'PPLA2']:
        score = 80   # 行政中心
    elif feature_code == 'AIRP':
        score = 70   # 机场
    
    # 基于人口的评分加成
    try:
        pop = int(float(population)) if population and str(population) != 'nan' else 0
        if pop > 1000000:
            score += 30
        elif pop > 100000:
            score += 20
        elif pop > 10000:
            score += 10
    except:
        pass
    
    return min(score, 100)

def format_population(population):
    """格式化人口数字"""
    try:
        pop = int(float(population)) if population and str(population) != 'nan' else 0
        if pop == 0:
            return ''
        elif pop >= 1000000:
            return f"{pop/1000000:.1f}百万"
        elif pop >= 10000:
            return f"{pop/10000:.1f}万"
        else:
            return f"{pop:,}"
    except:
        return ''

def process_business_data():
    """处理业务友好数据"""
    print("🚀 开始生成业务友好数据（简化版）")
    print("=" * 60)
    
    # 加载辅助数据
    feature_codes = load_feature_codes()
    admin_codes = load_admin_codes()
    country_info = load_country_info()
    
    # 定义列名
    columns = [
        'geonameid', 'name', 'asciiname', 'alternatenames',
        'latitude', 'longitude', 'feature_class', 'feature_code',
        'country_code', 'cc2', 'admin1_code', 'admin2_code',
        'admin3_code', 'admin4_code', 'population', 'elevation',
        'dem', 'timezone', 'modification_date'
    ]
    
    output_file = 'global_business_friendly_simple.csv'
    chunk_size = 100000
    chunk_num = 0
    total_processed = 0
    
    # 创建输出文件头
    header = [
        'geoname_id', 'name', 'ascii_name', 'coordinates',
        'feature_class', 'feature_code', 'feature_name', 'feature_category',
        'country_code', 'country_name', 'country_name_cn', 'continent_name',
        'admin1_code', 'admin1_name', 'admin_full_path',
        'population', 'population_formatted', 'elevation_formatted',
        'timezone', 'business_category', 'importance_score',
        'is_major_city', 'is_capital', 'is_tourist_attraction'
    ]
    
    # 写入头部
    with open(output_file, 'w', encoding='utf-8-sig') as f:
        f.write(','.join(header) + '\n')
    
    try:
        # 分块处理
        for chunk in pd.read_csv('allCountries.txt', sep='\t', names=columns, 
                               encoding='utf-8', chunksize=chunk_size, low_memory=False):
            chunk_num += 1
            print(f"🔄 处理第 {chunk_num} 块数据，共 {len(chunk)} 条记录...")
            
            processed_rows = []
            
            for _, row in chunk.iterrows():
                try:
                    # 安全获取值
                    geoname_id = str(row['geonameid']) if pd.notna(row['geonameid']) else ''
                    name = str(row['name']) if pd.notna(row['name']) else ''
                    ascii_name = str(row['asciiname']) if pd.notna(row['asciiname']) else ''
                    latitude = float(row['latitude']) if pd.notna(row['latitude']) else 0.0
                    longitude = float(row['longitude']) if pd.notna(row['longitude']) else 0.0
                    feature_class = str(row['feature_class']) if pd.notna(row['feature_class']) else ''
                    feature_code = str(row['feature_code']) if pd.notna(row['feature_code']) else ''
                    country_code = str(row['country_code']) if pd.notna(row['country_code']) else ''
                    admin1_code = str(row['admin1_code']) if pd.notna(row['admin1_code']) else ''
                    population = row['population'] if pd.notna(row['population']) else 0
                    elevation = int(row['elevation']) if pd.notna(row['elevation']) else None
                    timezone = str(row['timezone']) if pd.notna(row['timezone']) else ''
                    
                    # 获取增强信息
                    feature_name = feature_codes.get(f"{feature_class}.{feature_code}", feature_code)
                    feature_category = get_feature_category(feature_class)
                    
                    country_data = country_info.get(country_code, {})
                    country_name = country_data.get('name', '')
                    country_name_cn = get_country_name_cn(country_code)
                    continent_name = get_continent_name(country_data.get('continent', ''))
                    
                    admin1_name = admin_codes.get(f"{country_code}.{admin1_code}", admin1_code)
                    admin_full_path = f"{country_name} > {admin1_name}" if admin1_name else country_name
                    
                    business_category = get_business_category(feature_class, feature_code)
                    importance_score = calculate_importance_score(feature_code, population)
                    
                    # 业务标签
                    is_major_city = feature_code in ['PPLC', 'PPLA', 'PPLA2'] or (population and int(float(population)) > 100000)
                    is_capital = feature_code == 'PPLC'
                    is_tourist_attraction = feature_code in ['HTL', 'MUS', 'TMPL', 'MNMT', 'AIRP', 'MTRO']
                    
                    # 构建行数据
                    row_data = [
                        geoname_id, name, ascii_name, f"{latitude:.6f}, {longitude:.6f}",
                        feature_class, feature_code, feature_name, feature_category,
                        country_code, country_name, country_name_cn, continent_name,
                        admin1_code, admin1_name, admin_full_path,
                        str(population) if population else '', format_population(population),
                        f"{elevation}米" if elevation else '',
                        timezone, business_category, str(importance_score),
                        str(is_major_city), str(is_capital), str(is_tourist_attraction)
                    ]
                    
                    # 清理数据中的逗号和换行符
                    cleaned_row = []
                    for item in row_data:
                        cleaned_item = str(item).replace(',', '，').replace('\n', ' ').replace('\r', ' ')
                        cleaned_row.append(f'"{cleaned_item}"')
                    
                    processed_rows.append(','.join(cleaned_row))
                    
                except Exception as e:
                    continue
            
            # 写入文件
            with open(output_file, 'a', encoding='utf-8-sig') as f:
                for row in processed_rows:
                    f.write(row + '\n')
            
            total_processed += len(processed_rows)
            print(f"✅ 第 {chunk_num} 块处理完成，累计 {total_processed:,} 条记录")
            
            # 每10块输出进度
            if chunk_num % 10 == 0:
                print(f"📊 进度: 已处理 {chunk_num} 块，共 {total_processed:,} 条记录")
    
    except Exception as e:
        print(f"❌ 处理出错: {e}")
        return False
    
    print("=" * 60)
    print(f"🎉 业务友好数据生成完成！")
    print(f"📁 输出文件: {output_file}")
    print(f"📊 总记录数: {total_processed:,}")
    print(f"📦 处理块数: {chunk_num}")
    
    return True

if __name__ == "__main__":
    # 检查文件
    required_files = ['allCountries.txt', 'featureCodes_en.txt', 'admin1CodesASCII.txt', 'countryInfo.txt']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
    else:
        success = process_business_data()
        if success:
            print("\n🎯 业务友好数据特性:")
            print("  ✅ 中英文国家名称")
            print("  ✅ 特征分类和描述")
            print("  ✅ 行政区层级信息")
            print("  ✅ 业务分类标签")
            print("  ✅ 重要性评分")
            print("  ✅ 格式化的人口和海拔")
            print("  ✅ 业务标识（主要城市、首都、景点）")
