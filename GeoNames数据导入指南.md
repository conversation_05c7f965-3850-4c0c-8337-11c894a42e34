# GeoNames数据导入指南

## 📋 导入准备工作

### 1. 数据库表结构创建
```sql
-- 执行完整的表结构创建脚本
@create_geonames_complete_oracle_tables.sql
```

### 2. 数据文件下载
使用提供的下载工具获取所需数据文件：
```bash
python geonames_complete_downloader.py
```

### 3. 存储空间准备
- **表空间**: 至少15GB可用空间
- **临时表空间**: 至少5GB可用空间
- **UNDO表空间**: 至少2GB可用空间

## 📊 数据导入顺序

### 阶段1: 基础数据导入
1. **国家信息** (countryInfo.txt → GEONAMES_COUNTRIES)
2. **特征代码** (featureCodes_en.txt → GEONAMES_FEATURE_CODES)
3. **时区信息** (timeZones.txt → GEONAMES_TIMEZONES)
4. **行政区代码** (admin1CodesASCII.txt, admin2Codes.txt → GEONAMES_ADMIN_CODES)

### 阶段2: 主数据导入
5. **地理位置** (allCountries.txt 或各国ZIP文件 → GEONAMES_LOCATIONS)

### 阶段3: 扩展数据导入
6. **多语言别名** (alternateNamesV2.txt → GEONAMES_ALTERNATE_NAMES)
7. **层级关系** (hierarchy.txt → GEONAMES_HIERARCHY)

## 🛠️ 具体导入方法

### 方法1: SQL*Loader (推荐大文件)

#### 1.1 创建控制文件 - countries.ctl
```sql
LOAD DATA
INFILE 'countryInfo.txt'
INTO TABLE GEONAMES_COUNTRIES
FIELDS TERMINATED BY '\t'
TRAILING NULLCOLS
(
    ISO_CODE,
    ISO3_CODE,
    ISO_NUMERIC,
    FIPS_CODE,
    COUNTRY_NAME,
    CAPITAL,
    AREA_SQKM,
    POPULATION,
    CONTINENT,
    TLD,
    CURRENCY_CODE,
    CURRENCY_NAME,
    PHONE_PREFIX,
    POSTAL_CODE_FORMAT,
    POSTAL_CODE_REGEX,
    LANGUAGES,
    GEONAME_ID,
    NORTH_BOUND,
    SOUTH_BOUND,
    EAST_BOUND,
    WEST_BOUND
)
```

#### 1.2 执行导入命令
```bash
sqlldr userid=username/password@database control=countries.ctl log=countries.log bad=countries.bad
```

### 方法2: 外部表 (推荐中等文件)

#### 2.1 创建外部表
```sql
CREATE TABLE EXT_GEONAMES_LOCATIONS (
    GEONAME_ID          NUMBER(12),
    NAME                VARCHAR2(200),
    ASCII_NAME          VARCHAR2(200),
    ALTERNATE_NAMES     CLOB,
    LATITUDE            NUMBER(10,7),
    LONGITUDE           NUMBER(10,7),
    FEATURE_CLASS       CHAR(1),
    FEATURE_CODE        VARCHAR2(10),
    COUNTRY_CODE        CHAR(2),
    CC2                 VARCHAR2(200),
    ADMIN1_CODE         VARCHAR2(20),
    ADMIN2_CODE         VARCHAR2(80),
    ADMIN3_CODE         VARCHAR2(20),
    ADMIN4_CODE         VARCHAR2(20),
    POPULATION          NUMBER(12),
    ELEVATION           NUMBER(6),
    DEM                 NUMBER(6),
    TIMEZONE            VARCHAR2(40),
    MODIFICATION_DATE   DATE
)
ORGANIZATION EXTERNAL (
    TYPE ORACLE_LOADER
    DEFAULT DIRECTORY DATA_DIR
    ACCESS PARAMETERS (
        RECORDS DELIMITED BY NEWLINE
        FIELDS TERMINATED BY '\t'
        MISSING FIELD VALUES ARE NULL
    )
    LOCATION ('allCountries.txt')
)
REJECT LIMIT UNLIMITED;
```

#### 2.2 从外部表导入
```sql
INSERT /*+ APPEND */ INTO GEONAMES_LOCATIONS (
    GEONAME_ID, NAME, ASCII_NAME, ALTERNATE_NAMES,
    LATITUDE, LONGITUDE, FEATURE_CLASS, FEATURE_CODE,
    COUNTRY_CODE, CC2, ADMIN1_CODE, ADMIN2_CODE,
    ADMIN3_CODE, ADMIN4_CODE, POPULATION, ELEVATION,
    DEM, TIMEZONE, MODIFICATION_DATE
)
SELECT 
    GEONAME_ID, NAME, ASCII_NAME, ALTERNATE_NAMES,
    LATITUDE, LONGITUDE, FEATURE_CLASS, FEATURE_CODE,
    COUNTRY_CODE, CC2, ADMIN1_CODE, ADMIN2_CODE,
    ADMIN3_CODE, ADMIN4_CODE, POPULATION, ELEVATION,
    DEM, TIMEZONE, TO_DATE(MODIFICATION_DATE, 'YYYY-MM-DD')
FROM EXT_GEONAMES_LOCATIONS;

COMMIT;
```

### 方法3: PL/SQL批量导入 (小文件)

```sql
DECLARE
    TYPE t_bulk_collect IS TABLE OF EXT_GEONAMES_LOCATIONS%ROWTYPE;
    l_data t_bulk_collect;
    CURSOR c_data IS SELECT * FROM EXT_GEONAMES_LOCATIONS;
BEGIN
    OPEN c_data;
    LOOP
        FETCH c_data BULK COLLECT INTO l_data LIMIT 10000;
        
        FORALL i IN 1..l_data.COUNT
            INSERT INTO GEONAMES_LOCATIONS VALUES l_data(i);
        
        COMMIT;
        EXIT WHEN c_data%NOTFOUND;
    END LOOP;
    CLOSE c_data;
END;
/
```

## ⚡ 性能优化建议

### 导入前优化
```sql
-- 1. 禁用约束和触发器
ALTER TABLE GEONAMES_LOCATIONS DISABLE ALL TRIGGERS;
ALTER TABLE GEONAMES_ALTERNATE_NAMES DISABLE CONSTRAINT FK_ALTERNATE_GEONAME;

-- 2. 删除索引（导入后重建）
DROP INDEX IDX_GEONAMES_COORDINATES;
DROP INDEX IDX_GEONAMES_COUNTRY;
-- ... 其他索引

-- 3. 设置NOLOGGING模式
ALTER TABLE GEONAMES_LOCATIONS NOLOGGING;

-- 4. 增加内存参数
ALTER SESSION SET SORT_AREA_SIZE = 268435456;  -- 256MB
ALTER SESSION SET HASH_AREA_SIZE = 268435456;  -- 256MB
```

### 导入后优化
```sql
-- 1. 重新启用约束和触发器
ALTER TABLE GEONAMES_LOCATIONS ENABLE ALL TRIGGERS;
ALTER TABLE GEONAMES_ALTERNATE_NAMES ENABLE CONSTRAINT FK_ALTERNATE_GEONAME;

-- 2. 重建索引
CREATE INDEX IDX_GEONAMES_COORDINATES ON GEONAMES_LOCATIONS (LATITUDE, LONGITUDE);
CREATE INDEX IDX_GEONAMES_COUNTRY ON GEONAMES_LOCATIONS (COUNTRY_CODE);
-- ... 其他索引

-- 3. 收集统计信息
EXEC DBMS_STATS.GATHER_TABLE_STATS('SCHEMA_NAME', 'GEONAMES_LOCATIONS');
EXEC DBMS_STATS.GATHER_TABLE_STATS('SCHEMA_NAME', 'GEONAMES_ALTERNATE_NAMES');
-- ... 其他表

-- 4. 恢复LOGGING模式
ALTER TABLE GEONAMES_LOCATIONS LOGGING;
```

## 📋 数据验证检查

### 1. 执行数据质量检查
```sql
EXEC SP_CHECK_DATA_QUALITY;
```

### 2. 手动验证查询
```sql
-- 检查总记录数
SELECT COUNT(*) FROM GEONAMES_LOCATIONS;

-- 检查国家分布
SELECT COUNTRY_CODE, COUNT(*) 
FROM GEONAMES_LOCATIONS 
GROUP BY COUNTRY_CODE 
ORDER BY COUNT(*) DESC;

-- 检查特征分布
SELECT FEATURE_CLASS, FEATURE_CODE, COUNT(*) 
FROM GEONAMES_LOCATIONS 
GROUP BY FEATURE_CLASS, FEATURE_CODE 
ORDER BY COUNT(*) DESC;

-- 检查坐标范围
SELECT 
    MIN(LATITUDE) AS MIN_LAT,
    MAX(LATITUDE) AS MAX_LAT,
    MIN(LONGITUDE) AS MIN_LON,
    MAX(LONGITUDE) AS MAX_LON
FROM GEONAMES_LOCATIONS;

-- 检查外键完整性
SELECT COUNT(*) FROM GEONAMES_ALTERNATE_NAMES an
WHERE NOT EXISTS (
    SELECT 1 FROM GEONAMES_LOCATIONS l 
    WHERE l.GEONAME_ID = an.GEONAME_ID
);
```

## 🚨 常见问题解决

### 问题1: 字符编码问题
```sql
-- 设置正确的字符集
ALTER SESSION SET NLS_LANG='AMERICAN_AMERICA.UTF8';
```

### 问题2: 日期格式问题
```sql
-- 设置日期格式
ALTER SESSION SET NLS_DATE_FORMAT='YYYY-MM-DD';
```

### 问题3: 内存不足
```sql
-- 增加PGA内存
ALTER SESSION SET WORKAREA_SIZE_POLICY=MANUAL;
ALTER SESSION SET SORT_AREA_SIZE=536870912;  -- 512MB
```

### 问题4: 表空间不足
```sql
-- 扩展表空间
ALTER TABLESPACE USERS ADD DATAFILE 'users02.dbf' SIZE 5G;
```

## 📊 导入进度监控

### 监控导入进度
```sql
-- 查看当前会话状态
SELECT SID, SERIAL#, STATUS, SQL_ID, EVENT, SECONDS_IN_WAIT
FROM V$SESSION 
WHERE USERNAME = USER;

-- 查看长时间运行的操作
SELECT OPNAME, TARGET, SOFAR, TOTALWORK, 
       ROUND(SOFAR/TOTALWORK*100,2) AS PCT_COMPLETE,
       TIME_REMAINING
FROM V$SESSION_LONGOPS 
WHERE USERNAME = USER;
```

## 🎯 导入完成后的测试

### 功能测试查询
```sql
-- 测试城市视图
SELECT * FROM V_GEONAMES_CITIES 
WHERE COUNTRY_CODE = 'CN' AND POPULATION > 1000000;

-- 测试地标视图
SELECT * FROM V_GEONAMES_LANDMARKS 
WHERE COUNTRY_CODE = 'JP' AND FEATURE_CODE = 'HTL';

-- 测试国家统计
SELECT * FROM V_GEONAMES_COUNTRY_STATS 
WHERE CONTINENT = 'AS' 
ORDER BY TOTAL_LOCATIONS DESC;

-- 测试多语言查询
SELECT * FROM V_GEONAMES_MULTILINGUAL 
WHERE GEONAME_ID = 1816670 AND ISO_LANGUAGE IN ('zh', 'en', 'ja');
```

## 📝 导入日志记录

建议在导入过程中记录详细日志：
- 导入开始和结束时间
- 每个文件的记录数
- 错误和警告信息
- 性能统计信息

这样可以为后续的增量更新和问题排查提供参考。
