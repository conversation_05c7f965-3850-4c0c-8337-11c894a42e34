#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
业务友好数据使用示例
展示如何使用生成的业务友好数据进行各种业务场景的查询和分析
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json

class BusinessDataAnalyzer:
    """业务数据分析器"""
    
    def __init__(self, data_file='global_business_friendly_data.csv'):
        self.data_file = data_file
        self.df = None
        
    def load_data(self, sample_size=None):
        """加载业务友好数据"""
        print(f"📂 加载业务友好数据: {self.data_file}")
        
        try:
            if sample_size:
                self.df = pd.read_csv(self.data_file, nrows=sample_size, encoding='utf-8-sig')
                print(f"✅ 已加载样本数据: {len(self.df):,} 条记录")
            else:
                self.df = pd.read_csv(self.data_file, encoding='utf-8-sig')
                print(f"✅ 已加载完整数据: {len(self.df):,} 条记录")
                
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
        
        return True
    
    def demo_ecommerce_scenarios(self):
        """演示电商场景应用"""
        print("\n🛒 电商平台应用场景演示")
        print("=" * 60)
        
        # 1. 地址验证和标准化
        print("\n1. 地址验证和标准化")
        print("-" * 30)
        
        # 查找香港的主要城市
        hk_cities = self.df[
            (self.df['country_code'] == 'HK') & 
            (self.df['is_major_city'] == True)
        ].head(5)
        
        print("香港主要城市地址标准化:")
        for _, city in hk_cities.iterrows():
            print(f"  原始: {city['name']} ({city['feature_code']})")
            print(f"  标准: {city['admin_full_path']}")
            print(f"  坐标: {city['coordinates']}")
            print(f"  人口: {city['population_formatted']}")
            print()
        
        # 2. 配送区域划分
        print("2. 配送区域划分")
        print("-" * 30)
        
        # 按国家和重要性评分划分配送区域
        delivery_zones = self.df[
            self.df['country_code'].isin(['CN', 'HK', 'JP', 'KR'])
        ].groupby(['country_name', 'business_category']).agg({
            'geoname_id': 'count',
            'importance_score': 'mean'
        }).round(1)
        
        print("亚洲主要国家配送区域分析:")
        print(delivery_zones.head(10))
        
        # 3. 多语言支持
        print("\n3. 多语言地址显示")
        print("-" * 30)
        
        multilang_sample = self.df[
            (self.df['country_name_cn'] != '') & 
            (self.df['alternate_names'] != '')
        ].head(3)
        
        for _, location in multilang_sample.iterrows():
            print(f"地点: {location['name']}")
            print(f"中文: {location['country_name_cn']}")
            print(f"别名: {location['alternate_names'][:100]}...")
            print(f"完整路径: {location['admin_full_path']}")
            print()
    
    def demo_tourism_scenarios(self):
        """演示旅游场景应用"""
        print("\n🏖️ 旅游应用场景演示")
        print("=" * 60)
        
        # 1. 景点分类推荐
        print("\n1. 旅游景点分类推荐")
        print("-" * 30)
        
        # 查找旅游相关的地点
        tourist_attractions = self.df[
            self.df['is_tourist_attraction'] == True
        ].groupby(['country_name', 'feature_name']).size().head(10)
        
        print("全球主要旅游设施分布:")
        print(tourist_attractions)
        
        # 2. 时区信息展示
        print("\n2. 全球时区信息")
        print("-" * 30)
        
        timezone_info = self.df[
            self.df['timezone_formatted'] != ''
        ].groupby(['country_name', 'timezone_formatted']).agg({
            'geoname_id': 'count'
        }).head(10)
        
        print("主要国家时区分布:")
        print(timezone_info)
        
        # 3. 重要性评分排序
        print("\n3. 热门目的地推荐（按重要性评分）")
        print("-" * 30)
        
        top_destinations = self.df[
            (self.df['importance_score'] >= 80) &
            (self.df['business_category'].isin(['首都城市', '重要城市', '交通枢纽']))
        ].nlargest(10, 'importance_score')[
            ['name', 'country_name', 'business_category', 'importance_score', 'population_formatted']
        ]
        
        print("全球热门目的地TOP10:")
        print(top_destinations.to_string(index=False))
    
    def demo_logistics_scenarios(self):
        """演示物流场景应用"""
        print("\n📦 物流系统应用场景演示")
        print("=" * 60)
        
        # 1. 区域配送中心规划
        print("\n1. 区域配送中心规划")
        print("-" * 30)
        
        # 查找主要城市作为配送中心
        logistics_hubs = self.df[
            (self.df['business_category'].isin(['首都城市', '重要城市', '交通枢纽'])) &
            (self.df['population'] > 500000)
        ].groupby('continent_name').apply(
            lambda x: x.nlargest(3, 'importance_score')[['name', 'country_name', 'population_formatted', 'coordinates']]
        )
        
        print("各大洲推荐配送中心:")
        for continent, hubs in logistics_hubs.groupby(level=0):
            print(f"\n{continent}:")
            for _, hub in hubs.iterrows():
                print(f"  • {hub['name']}, {hub['country_name']} (人口: {hub['population_formatted']})")
        
        # 2. 时效计算支持
        print("\n2. 全球时区差异分析")
        print("-" * 30)
        
        timezone_analysis = self.df[
            self.df['gmt_offset'] != 0
        ].groupby('gmt_offset').agg({
            'country_name': lambda x: ', '.join(x.unique()[:3]),
            'geoname_id': 'count'
        }).head(10)
        
        print("主要时区分布:")
        print(timezone_analysis)
    
    def demo_financial_scenarios(self):
        """演示金融场景应用"""
        print("\n💰 金融服务应用场景演示")
        print("=" * 60)
        
        # 1. 合规地址验证
        print("\n1. 合规地址验证")
        print("-" * 30)
        
        # 显示完整的地理层级信息
        compliance_sample = self.df[
            (self.df['admin_full_path'] != '') &
            (self.df['country_name'] != '')
        ].head(5)
        
        print("地址合规验证示例:")
        for _, location in compliance_sample.iterrows():
            print(f"地点: {location['name']}")
            print(f"完整路径: {location['admin_full_path']}")
            print(f"国家信息: {location['country_name']} ({location['country_code']})")
            print(f"货币: {location['currency_name']} ({location['currency_code']})")
            print(f"语言: {location['languages']}")
            print()
        
        # 2. 风险评估支持
        print("2. 基于地理位置的风险评估")
        print("-" * 30)
        
        risk_analysis = self.df.groupby(['continent_name', 'country_name']).agg({
            'importance_score': 'mean',
            'geoname_id': 'count'
        }).round(1).head(15)
        
        print("各国地理重要性评估:")
        print(risk_analysis)
    
    def generate_api_examples(self):
        """生成API使用示例"""
        print("\n🔌 API接口使用示例")
        print("=" * 60)
        
        # 模拟API查询示例
        api_examples = {
            "地址搜索API": {
                "endpoint": "/api/locations/search",
                "method": "GET",
                "parameters": {
                    "query": "香港",
                    "country": "HK",
                    "feature_category": "居住地点",
                    "limit": 10
                },
                "response_sample": self.df[
                    (self.df['country_code'] == 'HK') & 
                    (self.df['feature_category'] == '居住地点')
                ].head(2).to_dict('records')
            },
            
            "地理编码API": {
                "endpoint": "/api/geocoding/reverse",
                "method": "POST", 
                "parameters": {
                    "latitude": 22.37137,
                    "longitude": 114.11329,
                    "radius": 1000
                },
                "response_sample": self.df[
                    (abs(self.df['latitude'] - 22.37137) < 0.01) &
                    (abs(self.df['longitude'] - 114.11329) < 0.01)
                ].head(1).to_dict('records')
            },
            
            "区域分析API": {
                "endpoint": "/api/analytics/region",
                "method": "GET",
                "parameters": {
                    "country_code": "CN",
                    "business_category": "重要城市",
                    "min_importance": 70
                },
                "response_sample": {
                    "total_count": len(self.df[
                        (self.df['country_code'] == 'CN') & 
                        (self.df['business_category'] == '重要城市') &
                        (self.df['importance_score'] >= 70)
                    ]),
                    "sample_locations": self.df[
                        (self.df['country_code'] == 'CN') & 
                        (self.df['business_category'] == '重要城市') &
                        (self.df['importance_score'] >= 70)
                    ].head(3).to_dict('records')
                }
            }
        }
        
        # 保存API示例
        with open('business_api_examples.json', 'w', encoding='utf-8') as f:
            json.dump(api_examples, f, ensure_ascii=False, indent=2, default=str)
        
        print("✅ API使用示例已保存到: business_api_examples.json")
        
        # 显示API概览
        for api_name, api_info in api_examples.items():
            print(f"\n{api_name}:")
            print(f"  端点: {api_info['endpoint']}")
            print(f"  方法: {api_info['method']}")
            print(f"  参数: {list(api_info['parameters'].keys())}")
    
    def run_all_demos(self):
        """运行所有演示"""
        print("🚀 业务友好数据应用场景全面演示")
        print("=" * 80)
        
        if not self.load_data(sample_size=50000):  # 加载5万条样本数据进行演示
            return
        
        # 运行各种场景演示
        self.demo_ecommerce_scenarios()
        self.demo_tourism_scenarios() 
        self.demo_logistics_scenarios()
        self.demo_financial_scenarios()
        self.generate_api_examples()
        
        print("\n🎯 演示总结")
        print("=" * 80)
        print("业务友好数据成功转换了原始GeoNames数据，提供了:")
        print("✅ 用户友好的地名和描述")
        print("✅ 完整的行政区层级信息")
        print("✅ 多语言和国际化支持")
        print("✅ 业务分类和重要性评分")
        print("✅ 时区和货币信息")
        print("✅ 适合各种业务场景的API接口")
        
        print(f"\n📊 数据统计:")
        print(f"  • 总记录数: {len(self.df):,}")
        print(f"  • 覆盖国家: {self.df['country_name'].nunique()}")
        print(f"  • 特征类别: {self.df['feature_category'].nunique()}")
        print(f"  • 业务分类: {self.df['business_category'].nunique()}")

def main():
    """主函数"""
    analyzer = BusinessDataAnalyzer()
    analyzer.run_all_demos()

if __name__ == "__main__":
    main()
