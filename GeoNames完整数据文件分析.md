# GeoNames完整数据文件分析报告

## 📋 文件分类概览

根据GeoNames网站分析，共有以下几类数据文件：

### 1. 🌍 国家/地区数据文件 (249个)
所有国家和地区的独立数据文件，格式为 `XX.zip`

### 2. 🏙️ 城市数据文件 (4个)
按人口规模筛选的城市数据

### 3. 📚 辅助数据文件 (15个)
行政区划、特征代码、时区等辅助信息

### 4. 🔄 增量更新文件 (4个)
每日更新的修改和删除记录

### 5. 🗺️ 地理边界文件 (2个)
国家边界的GeoJSON格式数据

## 📊 详细文件清单

### 🌍 国家/地区数据文件 (按文件大小排序)

| 文件名 | 大小 | 国家/地区 | 预估记录数 |
|--------|------|-----------|------------|
| **US.zip** | 68M | 美国 | ~2,000,000 |
| **CN.zip** | 30M | 中国 | ~943,000 |
| **NO.zip** | 15M | 挪威 | ~450,000 |
| **IN.zip** | 15M | 印度 | ~450,000 |
| **RU.zip** | 14M | 俄罗斯 | ~420,000 |
| **FI.zip** | 13M | 芬兰 | ~390,000 |
| **MX.zip** | 13M | 墨西哥 | ~390,000 |
| **IR.zip** | 13M | 伊朗 | ~390,000 |
| **ID.zip** | 9.9M | 印度尼西亚 | ~300,000 |
| **TH.zip** | 9.3M | 泰国 | ~280,000 |
| **CA.zip** | 7.7M | 加拿大 | ~230,000 |
| **FR.zip** | 6.9M | 法国 | ~210,000 |
| **BR.zip** | 6.8M | 巴西 | ~200,000 |
| **DE.zip** | 6.7M | 德国 | ~200,000 |
| **PK.zip** | 5.9M | 巴基斯坦 | ~180,000 |
| **AU.zip** | 5.4M | 澳大利亚 | ~160,000 |
| **KR.zip** | 5.2M | 韩国 | ~155,000 |
| **JP.zip** | 4.7M | 日本 | ~140,000 |
| **AF.zip** | 4.6M | 阿富汗 | ~140,000 |
| **IT.zip** | 4.1M | 意大利 | ~125,000 |

### 🏙️ 城市数据文件

| 文件名 | 大小 | 描述 | 预估记录数 |
|--------|------|------|------------|
| **cities500.zip** | 12M | 人口>500的城市 | ~185,000 |
| **cities1000.zip** | 9.3M | 人口>1000的城市 | ~130,000 |
| **cities5000.zip** | 4.8M | 人口>5000的城市 | ~50,000 |
| **cities15000.zip** | 2.8M | 人口>15000的城市 | ~25,000 |

### 📚 辅助数据文件

| 文件名 | 大小 | 描述 | 用途 |
|--------|------|------|------|
| **allCountries.zip** | 395M | 全球完整数据 | 包含所有国家数据 |
| **alternateNamesV2.zip** | 188M | 多语言别名数据 | 地名多语言支持 |
| **alternateNames.zip** | 187M | 旧版别名数据 | 即将废弃 |
| **admin2Codes.txt** | 2.3M | 二级行政区代码 | 行政区划对照 |
| **hierarchy.zip** | 2.0M | 地理层级关系 | 父子关系数据 |
| **admin1CodesASCII.txt** | 142K | 一级行政区代码 | 省/州级对照 |
| **adminCode5.zip** | 350K | 五级行政区代码 | 最细行政区划 |
| **userTags.zip** | 184K | 用户标签 | 用户贡献标签 |
| **iso-languagecodes.txt** | 135K | ISO语言代码 | 语言标准对照 |
| **featureCodes_en.txt** | 57K | 特征代码说明(英文) | 特征分类对照 |
| **featureCodes_ru.txt** | 113K | 特征代码说明(俄文) | 俄语特征对照 |
| **countryInfo.txt** | 31K | 国家信息 | 国家基本信息 |
| **timeZones.txt** | 14K | 时区信息 | 时区对照表 |
| **readme.txt** | 8.6K | 说明文档 | 数据格式说明 |
| **no-country.zip** | 253K | 无国家归属地点 | 国际水域等 |

### 🔄 增量更新文件

| 文件名 | 大小 | 描述 | 更新频率 |
|--------|------|------|----------|
| **modifications-2025-08-06.txt** | 42K | 昨日修改记录 | 每日 |
| **deletes-2025-08-06.txt** | 36B | 昨日删除记录 | 每日 |
| **alternateNamesModifications-2025-08-06.txt** | 18K | 别名修改记录 | 每日 |
| **alternateNamesDeletes-2025-08-06.txt** | 25B | 别名删除记录 | 每日 |

### 🗺️ 地理边界文件

| 文件名 | 大小 | 描述 | 格式 |
|--------|------|------|------|
| **shapes_all_low.zip** | 1.3M | 完整边界数据 | GeoJSON |
| **shapes_simplified_low.zip** | 1.2M | 简化边界数据 | GeoJSON |

## 📈 数据规模统计

### 总体规模
- **国家文件总数**: 249个
- **预估总记录数**: ~13,300,000条
- **压缩文件总大小**: ~800MB
- **解压后预估大小**: ~8GB

### 按大洲分布 (前20大文件)
| 大洲 | 文件数 | 总大小 | 主要国家 |
|------|--------|--------|----------|
| **北美洲** | 3 | 88.7M | 美国(68M)、加拿大(7.7M)、墨西哥(13M) |
| **亚洲** | 8 | 118.9M | 中国(30M)、印度(15M)、俄罗斯(14M) |
| **欧洲** | 6 | 49.1M | 挪威(15M)、芬兰(13M)、法国(6.9M) |
| **南美洲** | 1 | 6.8M | 巴西(6.8M) |
| **大洋洲** | 1 | 5.4M | 澳大利亚(5.4M) |
| **非洲** | 1 | 4.6M | 阿富汗(4.6M) |

## 🎯 下载优先级建议

### 🔥 高优先级 (核心数据)
1. **allCountries.zip** - 全球完整数据
2. **admin1CodesASCII.txt** - 一级行政区对照
3. **admin2Codes.txt** - 二级行政区对照
4. **countryInfo.txt** - 国家信息
5. **timeZones.txt** - 时区信息
6. **featureCodes_en.txt** - 特征代码说明

### 🌟 中优先级 (重要补充)
1. **alternateNamesV2.zip** - 多语言支持
2. **hierarchy.zip** - 地理层级关系
3. **cities1000.zip** - 主要城市数据
4. **userTags.zip** - 用户标签

### 📊 低优先级 (特殊需求)
1. **shapes_simplified_low.zip** - 地理边界
2. **adminCode5.zip** - 五级行政区
3. **no-country.zip** - 无国家归属地点
4. **增量更新文件** - 数据同步

### 🌍 按需下载 (国家数据)
根据业务需求选择特定国家：
- **亚太地区**: CN, JP, KR, IN, TH, AU
- **欧美地区**: US, CA, DE, FR, GB, IT
- **其他重点**: BR, RU, MX

## 💾 存储空间需求

### 压缩文件存储
- **全部下载**: ~800MB
- **核心文件**: ~600MB
- **主要国家(20个)**: ~300MB

### 解压后存储
- **全部解压**: ~8GB
- **核心数据**: ~2GB
- **主要国家**: ~1GB

### 数据库存储 (Oracle)
- **表空间需求**: ~15GB (包含索引)
- **临时空间**: ~5GB (导入时)
- **备份空间**: ~10GB

## 🔧 技术实现建议

### 下载策略
1. **分批下载**: 避免服务器压力
2. **断点续传**: 处理网络中断
3. **校验机制**: 确保文件完整性
4. **并发控制**: 限制同时下载数

### 数据处理
1. **流式处理**: 处理大文件
2. **分区导入**: 按国家分区
3. **增量更新**: 利用修改记录
4. **数据清洗**: 处理异常数据

### 存储优化
1. **分区表**: 按国家或特征分区
2. **压缩存储**: 启用表压缩
3. **索引策略**: 优化查询性能
4. **归档机制**: 历史数据归档

这个完整的文件分析为后续的批量下载和数据库设计提供了详细的基础信息。
