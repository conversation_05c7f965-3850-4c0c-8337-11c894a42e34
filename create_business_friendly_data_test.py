#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
业务友好数据生成器 - 测试版本
先测试1000条数据，确保没有错误后再处理完整文件
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
import os
from collections import defaultdict

class BusinessFriendlyDataGeneratorTest:
    """业务友好数据生成器测试版"""
    
    def __init__(self):
        self.feature_codes = {}
        self.admin_codes = {}
        self.country_info = {}
        self.timezone_info = {}
        
    def load_auxiliary_data(self):
        """加载所有辅助数据"""
        print("🔄 加载辅助数据...")
        
        # 1. 加载特征代码说明
        try:
            feature_df = pd.read_csv('featureCodes_en.txt', sep='\t', 
                                   names=['code', 'name', 'description'], 
                                   encoding='utf-8')
            for _, row in feature_df.iterrows():
                self.feature_codes[row['code']] = {
                    'name': str(row['name']) if pd.notna(row['name']) else '',
                    'description': str(row['description']) if pd.notna(row['description']) else ''
                }
            print(f"✅ 特征代码: {len(self.feature_codes)} 个")
        except Exception as e:
            print(f"⚠️ 特征代码加载失败: {e}")
        
        # 2. 加载行政区代码
        try:
            admin_df = pd.read_csv('admin1CodesASCII.txt', sep='\t',
                                 names=['code', 'name', 'asciiname', 'geonameid'],
                                 encoding='utf-8')
            for _, row in admin_df.iterrows():
                self.admin_codes[str(row['code'])] = {
                    'name': str(row['name']) if pd.notna(row['name']) else '',
                    'ascii_name': str(row['asciiname']) if pd.notna(row['asciiname']) else '',
                    'geonameid': str(row['geonameid']) if pd.notna(row['geonameid']) else ''
                }
            print(f"✅ 行政区代码: {len(self.admin_codes)} 个")
        except Exception as e:
            print(f"⚠️ 行政区代码加载失败: {e}")
        
        # 3. 加载国家信息
        try:
            country_data = []
            with open('countryInfo.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.startswith('#') and line.strip():
                        parts = line.strip().split('\t')
                        if len(parts) >= 16:
                            country_data.append(parts)
            
            for parts in country_data:
                iso_code = parts[0]
                self.country_info[iso_code] = {
                    'iso': parts[0],
                    'iso3': parts[1] if len(parts) > 1 else '',
                    'country_name': parts[4] if len(parts) > 4 else '',
                    'capital': parts[5] if len(parts) > 5 else '',
                    'area_sqkm': parts[6] if len(parts) > 6 else '',
                    'population': parts[7] if len(parts) > 7 else '',
                    'continent': parts[8] if len(parts) > 8 else '',
                    'currency_code': parts[10] if len(parts) > 10 else '',
                    'currency_name': parts[11] if len(parts) > 11 else '',
                    'languages': parts[15] if len(parts) > 15 else ''
                }
            print(f"✅ 国家信息: {len(self.country_info)} 个")
        except Exception as e:
            print(f"⚠️ 国家信息加载失败: {e}")
        
        # 4. 加载时区信息
        try:
            timezone_df = pd.read_csv('timeZones.txt', sep='\t',
                                    names=['country_code', 'timezone_id', 'gmt_offset', 'dst_offset', 'raw_offset'],
                                    encoding='utf-8')
            for _, row in timezone_df.iterrows():
                key = f"{row['country_code']}_{row['timezone_id']}"
                self.timezone_info[key] = {
                    'timezone_id': str(row['timezone_id']) if pd.notna(row['timezone_id']) else '',
                    'gmt_offset': float(row['gmt_offset']) if pd.notna(row['gmt_offset']) else 0.0,
                    'dst_offset': float(row['dst_offset']) if pd.notna(row['dst_offset']) else 0.0,
                    'raw_offset': float(row['raw_offset']) if pd.notna(row['raw_offset']) else 0.0
                }
            print(f"✅ 时区信息: {len(self.timezone_info)} 个")
        except Exception as e:
            print(f"⚠️ 时区信息加载失败: {e}")
    
    def safe_str(self, value):
        """安全转换为字符串"""
        if pd.isna(value) or value is None:
            return ''
        return str(value)
    
    def safe_float(self, value):
        """安全转换为浮点数"""
        if pd.isna(value) or value is None:
            return 0.0
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    
    def safe_int(self, value):
        """安全转换为整数"""
        if pd.isna(value) or value is None:
            return 0
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return 0
    
    def get_continent_name(self, continent_code):
        """获取大洲中文名称"""
        continent_map = {
            'AF': '非洲',
            'AS': '亚洲', 
            'EU': '欧洲',
            'NA': '北美洲',
            'OC': '大洋洲',
            'SA': '南美洲',
            'AN': '南极洲'
        }
        return continent_map.get(str(continent_code), str(continent_code) if continent_code else '')
    
    def get_feature_info(self, feature_class, feature_code):
        """获取特征信息"""
        full_code = f"{self.safe_str(feature_class)}.{self.safe_str(feature_code)}"
        if full_code in self.feature_codes:
            return self.feature_codes[full_code]
        return {'name': self.safe_str(feature_code), 'description': ''}
    
    def get_admin_name(self, country_code, admin_code):
        """获取行政区名称"""
        if pd.isna(admin_code) or admin_code == '' or admin_code == '00':
            return ''
        
        full_code = f"{self.safe_str(country_code)}.{self.safe_str(admin_code)}"
        if full_code in self.admin_codes:
            return self.admin_codes[full_code]['name']
        return self.safe_str(admin_code)
    
    def get_timezone_info(self, country_code, timezone_id):
        """获取时区信息"""
        key = f"{self.safe_str(country_code)}_{self.safe_str(timezone_id)}"
        if key in self.timezone_info:
            return self.timezone_info[key]
        return {'gmt_offset': 0.0, 'dst_offset': 0.0, 'raw_offset': 0.0}
    
    def format_population(self, population):
        """格式化人口数字"""
        pop = self.safe_int(population)
        if pop == 0:
            return ''
        
        if pop >= 1000000:
            return f"{pop/1000000:.1f}百万"
        elif pop >= 10000:
            return f"{pop/10000:.1f}万"
        else:
            return f"{pop:,}"
    
    def get_feature_category(self, feature_class):
        """获取特征大类别"""
        category_map = {
            'A': '行政区域',
            'H': '水文地理', 
            'L': '区域地带',
            'P': '居住地点',
            'R': '交通设施',
            'S': '建筑景点',
            'T': '地形地貌',
            'U': '海底地形',
            'V': '植被覆盖'
        }
        return category_map.get(str(feature_class), '其他')
    
    def get_country_name_cn(self, country_code):
        """获取国家中文名称"""
        cn_names = {
            'CN': '中国', 'HK': '香港', 'TW': '台湾', 'MO': '澳门',
            'JP': '日本', 'KR': '韩国', 'US': '美国', 'GB': '英国',
            'FR': '法国', 'DE': '德国', 'IT': '意大利', 'ES': '西班牙',
            'CA': '加拿大', 'AU': '澳大利亚', 'BR': '巴西', 'IN': '印度',
            'RU': '俄罗斯', 'MX': '墨西哥', 'TH': '泰国', 'SG': '新加坡'
        }
        return cn_names.get(str(country_code), '')
    
    def build_admin_path(self, country_name, admin1_name):
        """构建完整行政路径"""
        parts = []
        if country_name and country_name.strip():
            parts.append(str(country_name).strip())
        if admin1_name and admin1_name.strip():
            parts.append(str(admin1_name).strip())
        return ' > '.join(parts)
    
    def format_timezone(self, gmt_offset):
        """格式化时区显示"""
        offset = self.safe_float(gmt_offset)
        if offset == 0:
            return 'GMT'
        elif offset > 0:
            return f'GMT+{offset}'
        else:
            return f'GMT{offset}'
    
    def is_major_city(self, row):
        """判断是否为主要城市"""
        feature_code = self.safe_str(row['feature_code'])
        population = self.safe_int(row['population'])
        return (feature_code in ['PPLC', 'PPLA', 'PPLA2'] or population > 100000)
    
    def is_capital(self, row):
        """判断是否为首都"""
        return self.safe_str(row['feature_code']) == 'PPLC'
    
    def is_tourist_attraction(self, row):
        """判断是否为旅游景点"""
        tourist_codes = ['HTL', 'MUS', 'TMPL', 'MNMT', 'AIRP', 'MTRO']
        return self.safe_str(row['feature_code']) in tourist_codes
    
    def get_business_category(self, row):
        """获取业务分类"""
        feature_class = self.safe_str(row['feature_class'])
        feature_code = self.safe_str(row['feature_code'])
        
        if feature_class == 'P':
            if feature_code == 'PPLC':
                return '首都城市'
            elif feature_code in ['PPLA', 'PPLA2']:
                return '重要城市'
            else:
                return '一般城市'
        elif feature_class == 'S':
            if feature_code == 'HTL':
                return '酒店住宿'
            elif feature_code == 'AIRP':
                return '交通枢纽'
            else:
                return '服务设施'
        elif feature_class == 'T':
            return '自然景观'
        elif feature_class == 'H':
            return '水域地理'
        else:
            return '其他地点'
    
    def calculate_importance_score(self, row):
        """计算重要性评分"""
        score = 20  # 基础分
        
        feature_code = self.safe_str(row['feature_code'])
        population = self.safe_int(row['population'])
        
        # 基于特征代码的评分
        if feature_code == 'PPLC':
            score = 100  # 首都
        elif feature_code in ['PPLA', 'PPLA2']:
            score = 80   # 行政中心
        elif feature_code == 'AIRP':
            score = 70   # 机场
        
        # 基于人口的评分加成
        if population > 1000000:
            score += 30
        elif population > 100000:
            score += 20
        elif population > 10000:
            score += 10
        
        return min(score, 100)  # 最高100分
    
    def process_test_data(self, input_file='allCountries.txt', test_size=1000):
        """处理测试数据"""
        print(f"🧪 开始测试处理 {test_size} 条数据")
        print("=" * 60)
        
        # 加载辅助数据
        self.load_auxiliary_data()
        
        # 定义列名
        columns = [
            'geonameid', 'name', 'asciiname', 'alternatenames',
            'latitude', 'longitude', 'feature_class', 'feature_code',
            'country_code', 'cc2', 'admin1_code', 'admin2_code',
            'admin3_code', 'admin4_code', 'population', 'elevation',
            'dem', 'timezone', 'modification_date'
        ]
        
        try:
            # 读取测试数据
            print(f"📂 读取前 {test_size} 条记录...")
            df = pd.read_csv(input_file, sep='\t', names=columns, 
                           encoding='utf-8', nrows=test_size, low_memory=False)
            
            print(f"✅ 成功读取 {len(df)} 条记录")
            
            # 处理数据
            business_data = []
            error_count = 0
            
            for idx, row in df.iterrows():
                try:
                    # 获取国家信息
                    country_info = self.country_info.get(self.safe_str(row['country_code']), {})
                    
                    # 获取特征信息
                    feature_info = self.get_feature_info(row['feature_class'], row['feature_code'])
                    
                    # 获取行政区名称
                    admin1_name = self.get_admin_name(row['country_code'], row['admin1_code'])
                    
                    # 获取时区信息
                    timezone_info = self.get_timezone_info(row['country_code'], row['timezone'])
                    
                    # 构建业务友好记录
                    business_record = {
                        # 基本标识信息
                        'geoname_id': self.safe_int(row['geonameid']),
                        'name': self.safe_str(row['name']),
                        'ascii_name': self.safe_str(row['asciiname']),
                        'alternate_names': self.safe_str(row['alternatenames']),
                        
                        # 地理位置信息
                        'latitude': self.safe_float(row['latitude']),
                        'longitude': self.safe_float(row['longitude']),
                        'coordinates': f"{self.safe_float(row['latitude']):.6f}, {self.safe_float(row['longitude']):.6f}",
                        
                        # 特征分类信息（业务友好）
                        'feature_class': self.safe_str(row['feature_class']),
                        'feature_code': self.safe_str(row['feature_code']),
                        'feature_name': feature_info['name'],
                        'feature_description': feature_info['description'],
                        'feature_category': self.get_feature_category(row['feature_class']),
                        
                        # 国家和地区信息（业务友好）
                        'country_code': self.safe_str(row['country_code']),
                        'country_name': country_info.get('country_name', ''),
                        'country_name_cn': self.get_country_name_cn(row['country_code']),
                        'continent_code': country_info.get('continent', ''),
                        'continent_name': self.get_continent_name(country_info.get('continent', '')),
                        'capital': country_info.get('capital', ''),
                        'currency_code': country_info.get('currency_code', ''),
                        'currency_name': country_info.get('currency_name', ''),
                        'languages': country_info.get('languages', ''),
                        
                        # 行政区信息（业务友好）
                        'admin1_code': self.safe_str(row['admin1_code']),
                        'admin1_name': admin1_name,
                        'admin2_code': self.safe_str(row['admin2_code']),
                        'admin_full_path': self.build_admin_path(country_info.get('country_name', ''), admin1_name),
                        
                        # 人口和海拔信息（业务友好）
                        'population': self.safe_int(row['population']),
                        'population_formatted': self.format_population(row['population']),
                        'elevation': self.safe_int(row['elevation']) if pd.notna(row['elevation']) else None,
                        'elevation_formatted': f"{self.safe_int(row['elevation'])}米" if pd.notna(row['elevation']) else '',
                        
                        # 时区信息（业务友好）
                        'timezone': self.safe_str(row['timezone']),
                        'gmt_offset': timezone_info.get('gmt_offset', 0.0),
                        'timezone_formatted': self.format_timezone(timezone_info.get('gmt_offset', 0.0)),
                        
                        # 业务标签和分类
                        'is_major_city': self.is_major_city(row),
                        'is_capital': self.is_capital(row),
                        'is_tourist_attraction': self.is_tourist_attraction(row),
                        'business_category': self.get_business_category(row),
                        'importance_score': self.calculate_importance_score(row),
                        
                        # 元数据
                        'data_source': 'GeoNames',
                        'last_modified': self.safe_str(row['modification_date']),
                        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    business_data.append(business_record)
                    
                    # 每100条显示进度
                    if (idx + 1) % 100 == 0:
                        print(f"✅ 已处理 {idx + 1} 条记录")
                    
                except Exception as e:
                    error_count += 1
                    print(f"⚠️ 处理记录 {row['geonameid']} 时出错: {e}")
                    if error_count > 10:  # 如果错误太多，显示详细信息
                        print(f"   行数据: {dict(row)}")
                    continue
            
            # 转换为DataFrame并保存
            business_df = pd.DataFrame(business_data)
            output_file = f'test_business_friendly_data_{test_size}.csv'
            business_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print("=" * 60)
            print(f"🎉 测试完成！")
            print(f"📁 输出文件: {output_file}")
            print(f"📊 成功处理: {len(business_data)} 条记录")
            print(f"❌ 错误记录: {error_count} 条")
            print(f"✅ 成功率: {len(business_data)/(len(business_data)+error_count)*100:.1f}%")
            
            # 显示样本数据
            if len(business_df) > 0:
                print(f"\n📋 样本数据预览:")
                print(business_df[['name', 'country_name', 'feature_category', 'business_category', 'importance_score']].head())
                
                print(f"\n📈 数据统计:")
                print(f"  • 特征类别分布: {business_df['feature_category'].value_counts().to_dict()}")
                print(f"  • 业务分类分布: {business_df['business_category'].value_counts().to_dict()}")
                print(f"  • 主要国家分布: {business_df['country_name'].value_counts().head().to_dict()}")
            
            return len(business_data), error_count
            
        except Exception as e:
            print(f"❌ 测试处理失败: {e}")
            return 0, 1

def main():
    """主函数"""
    generator = BusinessFriendlyDataGeneratorTest()
    
    # 检查必要文件是否存在
    required_files = ['allCountries.txt', 'featureCodes_en.txt', 'admin1CodesASCII.txt', 
                     'countryInfo.txt', 'timeZones.txt']
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return
    
    # 测试处理1000条数据
    success_count, error_count = generator.process_test_data(test_size=1000)
    
    if success_count > 0 and error_count < success_count * 0.1:  # 错误率小于10%
        print(f"\n🎯 测试通过！可以处理完整数据集")
        
        # 询问是否处理完整数据
        user_input = input("\n是否继续处理完整数据集？(y/n): ").strip().lower()
        if user_input == 'y':
            print("开始处理完整数据集...")
            # 这里可以调用完整数据处理函数
        else:
            print("测试完成，可以查看测试结果文件")
    else:
        print(f"\n❌ 测试失败，错误率过高: {error_count/(success_count+error_count)*100:.1f}%")
        print("请检查数据格式和处理逻辑")

if __name__ == "__main__":
    main()
