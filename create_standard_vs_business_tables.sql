-- =====================================================================================
-- GeoNames标准表 vs 业务友好表对比设计
-- 创建日期: 2025-08-07
-- 目的: 展示原始数据与业务友好数据的价值对比
-- =====================================================================================

-- -- 删除已存在的表
-- BEGIN
--     FOR c IN (SELECT table_name FROM user_tables WHERE table_name IN ('GEONAMES_STANDARD', 'GEONAMES_BUSINESS_FRIENDLY')) LOOP
--         EXECUTE IMMEDIATE 'DROP TABLE ' || c.table_name || ' CASCADE CONSTRAINTS';
--         DBMS_OUTPUT.PUT_LINE('已删除表: ' || c.table_name);
--     END LOOP;
-- EXCEPTION
--     WHEN OTHERS THEN
--         NULL;
-- END;
-- /

-- -- 删除序列
-- BEGIN
--     FOR c IN (SELECT sequence_name FROM user_sequences WHERE sequence_name IN ('SEQ_STANDARD_ID', 'SEQ_BUSINESS_ID')) LOOP
--         EXECUTE IMMEDIATE 'DROP SEQUENCE ' || c.sequence_name;
--         DBMS_OUTPUT.PUT_LINE('已删除序列: ' || c.sequence_name);
--     END LOOP;
-- EXCEPTION
--     WHEN OTHERS THEN
--         NULL;
-- END;
-- /

-- =====================================================================================
-- 1. 创建标准表 - 完全按照GeoNames原始格式
-- =====================================================================================

CREATE TABLE GEONAMES_STANDARD (
    -- 内部管理字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID（自增）
    
    -- GeoNames原始字段（完全对应19列）
    GEONAME_ID          NUMBER(12)      NOT NULL,           -- GeoNames官方ID
    NAME                NVARCHAR2(200)  NOT NULL,           -- 地理位置名称
    ASCII_NAME          VARCHAR2(200),                      -- ASCII格式的地名
    ALTERNATE_NAMES     NCLOB,                              -- 别名列表（逗号分隔）
    LATITUDE            NUMBER(10,7)    NOT NULL,           -- 纬度
    LONGITUDE           NUMBER(10,7)    NOT NULL,           -- 经度
    FEATURE_CLASS       CHAR(1)         NOT NULL,           -- 特征类别（A/H/L/P/R/S/T/U/V）
    FEATURE_CODE        VARCHAR2(10)    NOT NULL,           -- 特征代码
    COUNTRY_CODE        CHAR(2)         NOT NULL,           -- 国家代码
    CC2                 VARCHAR2(200),                      -- 备用国家代码
    ADMIN1_CODE         VARCHAR2(20),                       -- 一级行政区代码
    ADMIN2_CODE         VARCHAR2(80),                       -- 二级行政区代码
    ADMIN3_CODE         VARCHAR2(20),                       -- 三级行政区代码
    ADMIN4_CODE         VARCHAR2(20),                       -- 四级行政区代码
    POPULATION          NUMBER(12),                         -- 人口数量
    ELEVATION           NUMBER(6),                          -- 海拔高度（米）
    DEM                 NUMBER(6),                          -- 数字高程模型
    TIMEZONE            VARCHAR2(40),                       -- IANA时区标识符
    MODIFICATION_DATE   DATE,                               -- 最后修改日期
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    
    -- 约束
    CONSTRAINT PK_GEONAMES_STANDARD PRIMARY KEY (ID),
    CONSTRAINT UK_STANDARD_GEONAME_ID UNIQUE (GEONAME_ID),
    CONSTRAINT CK_STANDARD_LATITUDE CHECK (LATITUDE BETWEEN -90 AND 90),
    CONSTRAINT CK_STANDARD_LONGITUDE CHECK (LONGITUDE BETWEEN -180 AND 180),
    CONSTRAINT CK_STANDARD_FEATURE_CLASS CHECK (FEATURE_CLASS IN ('A','H','L','P','R','S','T','U','V'))
);

-- 添加表注释
COMMENT ON TABLE GEONAMES_STANDARD IS '标准GeoNames数据表 - 完全按照原始数据格式存储，用于对比展示原始数据的特点';
COMMENT ON COLUMN GEONAMES_STANDARD.GEONAME_ID IS 'GeoNames官方ID - 全球唯一标识符';
COMMENT ON COLUMN GEONAMES_STANDARD.NAME IS '地理位置名称 - 原始名称，可能包含非ASCII字符';
COMMENT ON COLUMN GEONAMES_STANDARD.FEATURE_CLASS IS '特征类别 - A:行政区 H:水文 L:区域 P:居住地 R:交通 S:建筑 T:地形 U:海底 V:植被';
COMMENT ON COLUMN GEONAMES_STANDARD.FEATURE_CODE IS '特征代码 - 详细分类代码，如PPL、PPLA、HTL等';

-- =====================================================================================
-- 2. 创建业务友好表 - 增强的业务化字段
-- =====================================================================================

CREATE TABLE GEONAMES_BUSINESS_FRIENDLY (
    -- 内部管理字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID（自增）
    
    -- 基本标识信息（对应原始数据）
    GEONAME_ID          NUMBER(12)      NOT NULL,           -- GeoNames官方ID
    NAME                NVARCHAR2(200)  NOT NULL,           -- 地理位置名称
    ASCII_NAME          VARCHAR2(200),                      -- ASCII格式的地名
    ALTERNATE_NAMES     NCLOB,                              -- 别名列表
    
    -- 地理位置信息（增强版）
    LATITUDE            NUMBER(10,7)    NOT NULL,           -- 纬度
    LONGITUDE           NUMBER(10,7)    NOT NULL,           -- 经度
    COORDINATES_TEXT    VARCHAR2(50),                       -- 坐标文本格式 "lat, lng"
    
    -- 特征分类信息（业务友好）
    FEATURE_CLASS       CHAR(1)         NOT NULL,           -- 特征类别代码
    FEATURE_CODE        VARCHAR2(10)    NOT NULL,           -- 特征代码
    FEATURE_NAME        VARCHAR2(200),                      -- 特征名称（英文）
    FEATURE_NAME_CN     VARCHAR2(200),                      -- 特征名称（中文）
    FEATURE_CATEGORY    VARCHAR2(50),                       -- 特征大类别（中文）
    FEATURE_DESCRIPTION NVARCHAR2(500),                     -- 特征详细描述
    
    -- 国家和地区信息（业务友好）
    COUNTRY_CODE        CHAR(2)         NOT NULL,           -- 国家代码
    COUNTRY_NAME        NVARCHAR2(100),                     -- 国家名称（英文）
    COUNTRY_NAME_CN     NVARCHAR2(100),                     -- 国家名称（中文）
    CONTINENT_CODE      CHAR(2),                            -- 大洲代码
    CONTINENT_NAME      VARCHAR2(50),                       -- 大洲名称（中文）
    CAPITAL             NVARCHAR2(100),                     -- 首都
    CURRENCY_CODE       CHAR(3),                            -- 货币代码
    CURRENCY_NAME       VARCHAR2(100),                      -- 货币名称
    LANGUAGES           VARCHAR2(200),                      -- 语言列表
    
    -- 行政区信息（业务友好）
    ADMIN1_CODE         VARCHAR2(20),                       -- 一级行政区代码
    ADMIN1_NAME         NVARCHAR2(200),                     -- 一级行政区名称
    ADMIN2_CODE         VARCHAR2(80),                       -- 二级行政区代码
    ADMIN2_NAME         NVARCHAR2(200),                     -- 二级行政区名称
    ADMIN_FULL_PATH     NVARCHAR2(500),                     -- 完整行政路径
    
    -- 人口和海拔信息（业务友好）
    POPULATION          NUMBER(12),                         -- 人口数量
    POPULATION_FORMATTED VARCHAR2(50),                      -- 格式化人口显示
    POPULATION_LEVEL    VARCHAR2(20),                       -- 人口等级（大城市/中等城市/小城市）
    ELEVATION           NUMBER(6),                          -- 海拔高度（米）
    ELEVATION_FORMATTED VARCHAR2(50),                       -- 格式化海拔显示
    ELEVATION_LEVEL     VARCHAR2(20),                       -- 海拔等级（高原/山地/平原）
    
    -- 时区信息（业务友好）
    TIMEZONE            VARCHAR2(40),                       -- IANA时区标识符
    GMT_OFFSET          NUMBER(4,2),                        -- GMT偏移量
    TIMEZONE_FORMATTED  VARCHAR2(20),                       -- 格式化时区显示
    
    -- 业务标签和分类
    BUSINESS_CATEGORY   VARCHAR2(50),                       -- 业务分类
    IMPORTANCE_SCORE    NUMBER(3),                          -- 重要性评分（0-100）
    IS_MAJOR_CITY       CHAR(1)         DEFAULT 'N',       -- 是否主要城市
    IS_CAPITAL          CHAR(1)         DEFAULT 'N',       -- 是否首都
    IS_TOURIST_ATTRACTION CHAR(1)       DEFAULT 'N',       -- 是否旅游景点
    IS_TRANSPORT_HUB    CHAR(1)         DEFAULT 'N',       -- 是否交通枢纽
    IS_BUSINESS_CENTER  CHAR(1)         DEFAULT 'N',       -- 是否商业中心
    
    -- 扩展业务信息
    ECONOMIC_LEVEL      VARCHAR2(20),                       -- 经济发展水平
    TOURISM_RATING      NUMBER(2,1),                        -- 旅游评级（1-5星）
    SAFETY_LEVEL        VARCHAR2(20),                       -- 安全等级
    CLIMATE_TYPE        VARCHAR2(50),                       -- 气候类型
    
    -- 原始数据保留
    CC2                 VARCHAR2(200),                      -- 备用国家代码
    ADMIN3_CODE         VARCHAR2(20),                       -- 三级行政区代码
    ADMIN4_CODE         VARCHAR2(20),                       -- 四级行政区代码
    DEM                 NUMBER(6),                          -- 数字高程模型
    MODIFICATION_DATE   DATE,                               -- 最后修改日期
    
    -- 数据管理字段
    DATA_SOURCE         VARCHAR2(50)    DEFAULT 'GeoNames', -- 数据来源
    DATA_QUALITY_SCORE  NUMBER(3)       DEFAULT 100,       -- 数据质量评分
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    CREATED_BY          VARCHAR2(50)    DEFAULT USER,       -- 创建用户
    
    -- 约束
    CONSTRAINT PK_GEONAMES_BUSINESS PRIMARY KEY (ID),
    CONSTRAINT UK_BUSINESS_GEONAME_ID UNIQUE (GEONAME_ID),
    CONSTRAINT CK_BUSINESS_LATITUDE CHECK (LATITUDE BETWEEN -90 AND 90),
    CONSTRAINT CK_BUSINESS_LONGITUDE CHECK (LONGITUDE BETWEEN -180 AND 180),
    CONSTRAINT CK_BUSINESS_FEATURE_CLASS CHECK (FEATURE_CLASS IN ('A','H','L','P','R','S','T','U','V')),
    CONSTRAINT CK_BUSINESS_IMPORTANCE CHECK (IMPORTANCE_SCORE BETWEEN 0 AND 100),
    CONSTRAINT CK_BUSINESS_IS_MAJOR CHECK (IS_MAJOR_CITY IN ('Y','N')),
    CONSTRAINT CK_BUSINESS_IS_CAPITAL CHECK (IS_CAPITAL IN ('Y','N')),
    CONSTRAINT CK_BUSINESS_IS_TOURIST CHECK (IS_TOURIST_ATTRACTION IN ('Y','N')),
    CONSTRAINT CK_BUSINESS_IS_TRANSPORT CHECK (IS_TRANSPORT_HUB IN ('Y','N')),
    CONSTRAINT CK_BUSINESS_IS_CENTER CHECK (IS_BUSINESS_CENTER IN ('Y','N'))
);

-- 添加表注释
COMMENT ON TABLE GEONAMES_BUSINESS_FRIENDLY IS '业务友好GeoNames数据表 - 增强的业务化字段，提供用户友好的数据展示和查询';
COMMENT ON COLUMN GEONAMES_BUSINESS_FRIENDLY.FEATURE_NAME_CN IS '特征名称中文 - 如"居住地"、"酒店"、"机场"等';
COMMENT ON COLUMN GEONAMES_BUSINESS_FRIENDLY.BUSINESS_CATEGORY IS '业务分类 - 如"首都城市"、"重要城市"、"旅游景点"等';
COMMENT ON COLUMN GEONAMES_BUSINESS_FRIENDLY.IMPORTANCE_SCORE IS '重要性评分 - 基于人口、特征类型等计算的综合评分';

-- =====================================================================================
-- 3. 创建序列
-- =====================================================================================

CREATE SEQUENCE SEQ_STANDARD_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;
CREATE SEQUENCE SEQ_BUSINESS_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;

-- =====================================================================================
-- 4. 创建触发器
-- =====================================================================================

-- 标准表触发器
CREATE OR REPLACE TRIGGER TRG_STANDARD_BI
    BEFORE INSERT ON GEONAMES_STANDARD
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_STANDARD_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

CREATE OR REPLACE TRIGGER TRG_STANDARD_BU
    BEFORE UPDATE ON GEONAMES_STANDARD
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- 业务友好表触发器
CREATE OR REPLACE TRIGGER TRG_BUSINESS_BI
    BEFORE INSERT ON GEONAMES_BUSINESS_FRIENDLY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_BUSINESS_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
    
    -- 自动生成坐标文本
    IF :NEW.COORDINATES_TEXT IS NULL THEN
        :NEW.COORDINATES_TEXT := :NEW.LATITUDE || ', ' || :NEW.LONGITUDE;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER TRG_BUSINESS_BU
    BEFORE UPDATE ON GEONAMES_BUSINESS_FRIENDLY
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_DATE := SYSDATE;
    
    -- 更新坐标文本
    :NEW.COORDINATES_TEXT := :NEW.LATITUDE || ', ' || :NEW.LONGITUDE;
END;
/

-- =====================================================================================
-- 5. 创建索引
-- =====================================================================================

-- 标准表索引
CREATE INDEX IDX_STANDARD_COUNTRY ON GEONAMES_STANDARD (COUNTRY_CODE);
CREATE INDEX IDX_STANDARD_FEATURE ON GEONAMES_STANDARD (FEATURE_CLASS, FEATURE_CODE);
CREATE INDEX IDX_STANDARD_COORDS ON GEONAMES_STANDARD (LATITUDE, LONGITUDE);
CREATE INDEX IDX_STANDARD_NAME ON GEONAMES_STANDARD (NAME);

-- 业务友好表索引
CREATE INDEX IDX_BUSINESS_COUNTRY ON GEONAMES_BUSINESS_FRIENDLY (COUNTRY_CODE);
CREATE INDEX IDX_BUSINESS_CATEGORY ON GEONAMES_BUSINESS_FRIENDLY (BUSINESS_CATEGORY);
CREATE INDEX IDX_BUSINESS_IMPORTANCE ON GEONAMES_BUSINESS_FRIENDLY (IMPORTANCE_SCORE);
CREATE INDEX IDX_BUSINESS_COORDS ON GEONAMES_BUSINESS_FRIENDLY (LATITUDE, LONGITUDE);
CREATE INDEX IDX_BUSINESS_NAME ON GEONAMES_BUSINESS_FRIENDLY (NAME);
CREATE INDEX IDX_BUSINESS_MAJOR_CITY ON GEONAMES_BUSINESS_FRIENDLY (IS_MAJOR_CITY);
CREATE INDEX IDX_BUSINESS_TOURIST ON GEONAMES_BUSINESS_FRIENDLY (IS_TOURIST_ATTRACTION);

-- =====================================================================================
-- 6. 创建对比视图
-- =====================================================================================

-- -- 数据对比视图
-- CREATE OR REPLACE VIEW V_DATA_COMPARISON AS
-- SELECT 
--     'STANDARD' as TABLE_TYPE,
--     COUNT(*) as RECORD_COUNT,
--     COUNT(DISTINCT COUNTRY_CODE) as COUNTRY_COUNT,
--     COUNT(DISTINCT FEATURE_CLASS) as FEATURE_CLASS_COUNT,
--     COUNT(CASE WHEN POPULATION > 0 THEN 1 END) as HAS_POPULATION_COUNT,
--     ROUND(AVG(CASE WHEN POPULATION > 0 THEN POPULATION END), 0) as AVG_POPULATION
-- FROM GEONAMES_STANDARD
-- UNION ALL
-- SELECT 
--     'BUSINESS_FRIENDLY' as TABLE_TYPE,
--     COUNT(*) as RECORD_COUNT,
--     COUNT(DISTINCT COUNTRY_CODE) as COUNTRY_COUNT,
--     COUNT(DISTINCT FEATURE_CATEGORY) as FEATURE_CLASS_COUNT,
--     COUNT(CASE WHEN POPULATION > 0 THEN 1 END) as HAS_POPULATION_COUNT,
--     ROUND(AVG(CASE WHEN POPULATION > 0 THEN POPULATION END), 0) as AVG_POPULATION
-- FROM GEONAMES_BUSINESS_FRIENDLY;

-- -- 业务价值展示视图
-- CREATE OR REPLACE VIEW V_BUSINESS_VALUE_DEMO AS
-- SELECT 
--     s.GEONAME_ID,
--     s.NAME as STANDARD_NAME,
--     s.FEATURE_CLASS || '.' || s.FEATURE_CODE as STANDARD_FEATURE,
--     s.COUNTRY_CODE as STANDARD_COUNTRY,
--     s.ADMIN1_CODE as STANDARD_ADMIN1,
--     s.POPULATION as STANDARD_POPULATION,
--     '---对比---' as SEPARATOR,
--     b.NAME as BUSINESS_NAME,
--     b.FEATURE_NAME_CN as BUSINESS_FEATURE_CN,
--     b.COUNTRY_NAME_CN as BUSINESS_COUNTRY_CN,
--     b.ADMIN_FULL_PATH as BUSINESS_FULL_PATH,
--     b.POPULATION_FORMATTED as BUSINESS_POPULATION,
--     b.BUSINESS_CATEGORY,
--     b.IMPORTANCE_SCORE,
--     CASE WHEN b.IS_MAJOR_CITY = 'Y' THEN '主要城市' ELSE '' END as CITY_TYPE,
--     CASE WHEN b.IS_TOURIST_ATTRACTION = 'Y' THEN '旅游景点' ELSE '' END as TOURIST_TYPE
-- FROM GEONAMES_STANDARD s
-- LEFT JOIN GEONAMES_BUSINESS_FRIENDLY b ON s.GEONAME_ID = b.GEONAME_ID
-- WHERE ROWNUM <= 100;  -- 限制显示前100条用于演示

-- =====================================================================================
-- 输出创建完成信息
-- =====================================================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
    DBMS_OUTPUT.PUT_LINE('GeoNames标准表 vs 业务友好表创建完成！');
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
    DBMS_OUTPUT.PUT_LINE('已创建的对象:');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('📊 数据表 (2个):');
    DBMS_OUTPUT.PUT_LINE('  1. GEONAMES_STANDARD - 标准原始数据表 (21个字段)');
    DBMS_OUTPUT.PUT_LINE('  2. GEONAMES_BUSINESS_FRIENDLY - 业务友好数据表 (45个字段)');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('🔢 序列 (2个): SEQ_STANDARD_ID, SEQ_BUSINESS_ID');
    DBMS_OUTPUT.PUT_LINE('⚡ 触发器 (4个): 自动主键和时间戳维护');
    DBMS_OUTPUT.PUT_LINE('📇 索引 (11个): 优化查询性能');
    DBMS_OUTPUT.PUT_LINE('👁️ 视图 (2个): 数据对比视图、业务价值展示视图');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('🎯 对比要点:');
    DBMS_OUTPUT.PUT_LINE('  • 标准表: 21个字段，完全对应原始数据');
    DBMS_OUTPUT.PUT_LINE('  • 业务表: 45个字段，增加24个业务友好字段');
    DBMS_OUTPUT.PUT_LINE('  • 增强功能: 中文名称、业务分类、重要性评分、标签化');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('📋 使用示例:');
    DBMS_OUTPUT.PUT_LINE('  - SELECT * FROM V_DATA_COMPARISON;  -- 查看数据对比');
    DBMS_OUTPUT.PUT_LINE('  - SELECT * FROM V_BUSINESS_VALUE_DEMO;  -- 查看业务价值对比');
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
END;
/
