#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeoNames完整数据下载器
支持下载所有类型的GeoNames数据文件
"""

import os
import requests
import zipfile
import time
import json
from urllib.parse import urljoin
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

class GeoNamesCompleteDownloader:
    """GeoNames完整数据下载器"""
    
    def __init__(self, base_url="https://download.geonames.org/export/dump/"):
        self.base_url = base_url
        self.download_dir = "geonames_complete_data"
        self.max_workers = 3  # 限制并发数，避免服务器压力
        self.delay_between_downloads = 2  # 下载间隔(秒)
        
        # 创建下载目录
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
    
    def get_all_files_info(self):
        """获取所有文件的详细信息"""
        files_info = {
            # 核心数据文件
            'core_files': [
                {'name': 'allCountries.zip', 'size': '395M', 'priority': 1, 'description': '全球完整地理数据'},
                {'name': 'admin1CodesASCII.txt', 'size': '142K', 'priority': 1, 'description': '一级行政区代码对照表'},
                {'name': 'admin2Codes.txt', 'size': '2.3M', 'priority': 1, 'description': '二级行政区代码对照表'},
                {'name': 'countryInfo.txt', 'size': '31K', 'priority': 1, 'description': '国家基本信息'},
                {'name': 'timeZones.txt', 'size': '14K', 'priority': 1, 'description': '时区信息对照表'},
                {'name': 'featureCodes_en.txt', 'size': '57K', 'priority': 1, 'description': '特征代码说明(英文)'},
                {'name': 'readme.txt', 'size': '8.6K', 'priority': 1, 'description': '数据格式说明文档'},
            ],
            
            # 城市数据文件
            'cities_files': [
                {'name': 'cities500.zip', 'size': '12M', 'priority': 2, 'description': '人口>500的城市'},
                {'name': 'cities1000.zip', 'size': '9.3M', 'priority': 2, 'description': '人口>1000的城市'},
                {'name': 'cities5000.zip', 'size': '4.8M', 'priority': 2, 'description': '人口>5000的城市'},
                {'name': 'cities15000.zip', 'size': '2.8M', 'priority': 2, 'description': '人口>15000的城市'},
            ],
            
            # 辅助数据文件
            'auxiliary_files': [
                {'name': 'alternateNamesV2.zip', 'size': '188M', 'priority': 2, 'description': '多语言地名别名'},
                {'name': 'hierarchy.zip', 'size': '2.0M', 'priority': 2, 'description': '地理层级关系'},
                {'name': 'userTags.zip', 'size': '184K', 'priority': 3, 'description': '用户贡献标签'},
                {'name': 'adminCode5.zip', 'size': '350K', 'priority': 3, 'description': '五级行政区代码'},
                {'name': 'iso-languagecodes.txt', 'size': '135K', 'priority': 2, 'description': 'ISO语言代码'},
                {'name': 'no-country.zip', 'size': '253K', 'priority': 3, 'description': '无国家归属地点'},
            ],
            
            # 地理边界文件
            'boundary_files': [
                {'name': 'shapes_simplified_low.zip', 'size': '1.2M', 'priority': 3, 'description': '简化国家边界'},
                {'name': 'shapes_all_low.zip', 'size': '1.3M', 'priority': 3, 'description': '完整国家边界'},
            ],
            
            # 主要国家、城市数据文件
            'major_countries': [
                {'name': 'US.zip', 'size': '68M', 'country': '美国', 'priority': 1},
                {'name': 'CN.zip', 'size': '30M', 'country': '中国', 'priority': 1},
                {'name': 'IN.zip', 'size': '15M', 'country': '印度', 'priority': 2},
                {'name': 'RU.zip', 'size': '14M', 'country': '俄罗斯', 'priority': 2},
                {'name': 'JP.zip', 'size': '4.7M', 'country': '日本', 'priority': 1},
                {'name': 'DE.zip', 'size': '6.7M', 'country': '德国', 'priority': 2},
                {'name': 'FR.zip', 'size': '6.9M', 'country': '法国', 'priority': 2},
                {'name': 'GB.zip', 'size': '3.5M', 'country': '英国', 'priority': 2},
                {'name': 'CA.zip', 'size': '7.7M', 'country': '加拿大', 'priority': 2},
                {'name': 'AU.zip', 'size': '5.4M', 'country': '澳大利亚', 'priority': 2},
                {'name': 'BR.zip', 'size': '6.8M', 'country': '巴西', 'priority': 2},
                {'name': 'KR.zip', 'size': '5.2M', 'country': '韩国', 'priority': 2},
                {'name': 'IT.zip', 'size': '4.1M', 'country': '意大利', 'priority': 2},
                {'name': 'HK.zip', 'size': '116K', 'country': '香港', 'priority': 1},
                {'name': 'TW.zip', 'size': '2.2M', 'country': '台湾', 'priority': 2},
            ]
        }
        
        # 生成所有国家代码列表
        all_countries = [
            'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AN', 'AO', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AW', 'AX', 'AZ',
            'BA', 'BB', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT', 'BV', 'BW', 'BY', 'BZ',
            'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM', 'CN', 'CO', 'CR', 'CS', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ',
            'DE', 'DJ', 'DK', 'DM', 'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK', 'FM', 'FO', 'FR',
            'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN', 'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY',
            'HK', 'HM', 'HN', 'HR', 'HT', 'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM', 'JO', 'JP',
            'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY', 'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY',
            'MA', 'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ', 'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ',
            'NA', 'NC', 'NE', 'NF', 'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG', 'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY',
            'QA', 'RE', 'RO', 'RS', 'RU', 'RW', 'SA', 'SB', 'SC', 'SD', 'SE', 'SG', 'SH', 'SI', 'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX', 'SY', 'SZ',
            'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO', 'TR', 'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ',
            'VA', 'VC', 'VE', 'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'XK', 'YE', 'YT', 'YU', 'ZA', 'ZM', 'ZW'
        ]
        
        files_info['all_countries'] = [{'name': f'{code}.zip', 'country': code, 'priority': 3} for code in all_countries]
        
        return files_info
    
    def download_file(self, filename, show_progress=True):
        """下载单个文件"""
        url = urljoin(self.base_url, filename)
        local_path = os.path.join(self.download_dir, filename)
        
        # 检查文件是否已存在
        if os.path.exists(local_path):
            print(f"✓ 文件 {filename} 已存在，跳过下载")
            return local_path
        
        try:
            print(f"🔄 开始下载: {filename}")
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if show_progress and total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r📥 {filename}: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
            
            if show_progress:
                print(f"\n✅ 下载完成: {filename}")
            
            return local_path
            
        except Exception as e:
            print(f"❌ 下载失败 {filename}: {e}")
            # 删除不完整的文件
            if os.path.exists(local_path):
                os.remove(local_path)
            return None
    
    def download_by_category(self, category, extract=True):
        """按类别下载文件"""
        files_info = self.get_all_files_info()
        
        if category not in files_info:
            print(f"❌ 未知类别: {category}")
            return
        
        files = files_info[category]
        print(f"📦 开始下载 {category} 类别，共 {len(files)} 个文件")
        
        success_count = 0
        for i, file_info in enumerate(files):
            filename = file_info['name']
            print(f"\n[{i+1}/{len(files)}] 处理文件: {filename}")
            
            # 下载文件
            result = self.download_file(filename)
            if result:
                success_count += 1
                
                # 如果是ZIP文件且需要解压
                if extract and filename.endswith('.zip'):
                    self.extract_zip(result)
            
            # 添加延迟
            if i < len(files) - 1:
                time.sleep(self.delay_between_downloads)
        
        print(f"\n📊 下载完成: {success_count}/{len(files)} 个文件成功")
    
    def download_priority_files(self, priority_level=1, extract=True):
        """按优先级下载文件"""
        files_info = self.get_all_files_info()
        priority_files = []
        
        # 收集指定优先级的文件
        for category, files in files_info.items():
            if category == 'all_countries':
                continue  # 跳过所有国家，单独处理
            
            for file_info in files:
                if file_info.get('priority', 3) <= priority_level:
                    priority_files.append(file_info)
        
        print(f"🎯 开始下载优先级 {priority_level} 的文件，共 {len(priority_files)} 个")
        
        success_count = 0
        for i, file_info in enumerate(priority_files):
            filename = file_info['name']
            description = file_info.get('description', file_info.get('country', ''))
            print(f"\n[{i+1}/{len(priority_files)}] {filename} - {description}")
            
            result = self.download_file(filename)
            if result:
                success_count += 1
                
                if extract and filename.endswith('.zip'):
                    self.extract_zip(result)
            
            time.sleep(self.delay_between_downloads)
        
        print(f"\n📊 优先级下载完成: {success_count}/{len(priority_files)} 个文件成功")
    
    def download_countries(self, country_codes, extract=True):
        """下载指定国家的数据"""
        if isinstance(country_codes, str):
            country_codes = [country_codes]
        
        print(f"🌍 开始下载 {len(country_codes)} 个国家的数据")
        
        success_count = 0
        for i, code in enumerate(country_codes):
            filename = f"{code.upper()}.zip"
            print(f"\n[{i+1}/{len(country_codes)}] 下载国家: {code.upper()}")
            
            result = self.download_file(filename)
            if result:
                success_count += 1
                
                if extract:
                    self.extract_zip(result)
            
            time.sleep(self.delay_between_downloads)
        
        print(f"\n📊 国家数据下载完成: {success_count}/{len(country_codes)} 个成功")
    
    def extract_zip(self, zip_path):
        """解压ZIP文件"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.download_dir)
            print(f"📂 解压完成: {os.path.basename(zip_path)}")
            return True
        except Exception as e:
            print(f"❌ 解压失败 {zip_path}: {e}")
            return False
    
    def get_download_summary(self):
        """获取下载摘要"""
        if not os.path.exists(self.download_dir):
            return "下载目录不存在"
        
        files = os.listdir(self.download_dir)
        zip_files = [f for f in files if f.endswith('.zip')]
        txt_files = [f for f in files if f.endswith('.txt')]
        
        total_size = 0
        for file in files:
            file_path = os.path.join(self.download_dir, file)
            if os.path.isfile(file_path):
                total_size += os.path.getsize(file_path)
        
        summary = f"""
📊 下载摘要
{'='*50}
📁 下载目录: {self.download_dir}
📦 ZIP文件: {len(zip_files)} 个
📄 TXT文件: {len(txt_files)} 个
📏 总大小: {total_size / 1024 / 1024:.2f} MB
📅 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        return summary

def main():
    """主函数 - 交互式下载"""
    downloader = GeoNamesCompleteDownloader()
    
    print("🌍 GeoNames完整数据下载器")
    print("=" * 50)
    
    while True:
        print("\n请选择下载选项:")
        print("1. 下载核心数据文件 (优先级1)")
        print("2. 下载重要数据文件 (优先级1-2)")
        print("3. 下载所有辅助文件 (优先级1-3)")
        print("4. 下载指定国家数据")
        print("5. 下载主要国家数据")
        print("6. 下载城市数据文件")
        print("7. 下载地理边界文件")
        print("8. 查看下载摘要")
        print("9. 退出")
        
        choice = input("\n请输入选择 (1-9): ").strip()
        
        if choice == '1':
            downloader.download_priority_files(priority_level=1)
        
        elif choice == '2':
            downloader.download_priority_files(priority_level=2)
        
        elif choice == '3':
            downloader.download_priority_files(priority_level=3)
        
        elif choice == '4':
            codes = input("请输入国家代码，用逗号分隔 (如 CN,JP,US): ").strip()
            if codes:
                country_codes = [code.strip().upper() for code in codes.split(',')]
                downloader.download_countries(country_codes)
        
        elif choice == '5':
            major_countries = ['US', 'CN', 'JP', 'DE', 'FR', 'GB', 'CA', 'AU', 'HK']
            downloader.download_countries(major_countries)
        
        elif choice == '6':
            downloader.download_by_category('cities_files')
        
        elif choice == '7':
            downloader.download_by_category('boundary_files')
        
        elif choice == '8':
            print(downloader.get_download_summary())
        
        elif choice == '9':
            print("👋 感谢使用GeoNames下载器！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
