-- =====================================================================================
-- 业务友好Oracle视图和存储过程创建脚本
-- 基于已有的GeoNames数据库表创建业务友好的视图和查询接口
-- =====================================================================================

-- =====================================================================================
-- 1. 创建业务友好的主视图
-- =====================================================================================

CREATE OR REPLACE VIEW V_BUSINESS_LOCATIONS AS
SELECT 
    -- 基本标识信息
    l.ID as internal_id,
    l.GEONAME_ID as geoname_id,
    l.NAME as name,
    l.ASCII_NAME as ascii_name,
    l.ALTERNATE_NAMES as alternate_names,
    
    -- 地理位置信息
    l.LATITUDE as latitude,
    l.LONGITUDE as longitude,
    l.LATITUDE || ', ' || l.LONGITUDE as coordinates,
    
    -- 特征分类信息（业务友好）
    l.FEATURE_CLASS as feature_class,
    l.FEATURE_CODE as feature_code,
    NVL(fc.NAME, l.FEATURE_CODE) as feature_name,
    NVL(fc.DESCRIPTION, '') as feature_description,
    CASE l.FEATURE_CLASS
        WHEN 'A' THEN '行政区域'
        WHEN 'H' THEN '水文地理'
        WHEN 'L' THEN '区域地带'
        WHEN 'P' THEN '居住地点'
        WHEN 'R' THEN '交通设施'
        WHEN 'S' THEN '建筑景点'
        WHEN 'T' THEN '地形地貌'
        WHEN 'U' THEN '海底地形'
        WHEN 'V' THEN '植被覆盖'
        ELSE '其他'
    END as feature_category,
    
    -- 国家和地区信息（业务友好）
    l.COUNTRY_CODE as country_code,
    NVL(c.COUNTRY_NAME, l.COUNTRY_CODE) as country_name,
    CASE l.COUNTRY_CODE
        WHEN 'CN' THEN '中国'
        WHEN 'HK' THEN '香港'
        WHEN 'TW' THEN '台湾'
        WHEN 'MO' THEN '澳门'
        WHEN 'JP' THEN '日本'
        WHEN 'KR' THEN '韩国'
        WHEN 'US' THEN '美国'
        WHEN 'GB' THEN '英国'
        WHEN 'FR' THEN '法国'
        WHEN 'DE' THEN '德国'
        WHEN 'CA' THEN '加拿大'
        WHEN 'AU' THEN '澳大利亚'
        ELSE ''
    END as country_name_cn,
    NVL(c.CONTINENT, '') as continent_code,
    CASE NVL(c.CONTINENT, '')
        WHEN 'AF' THEN '非洲'
        WHEN 'AS' THEN '亚洲'
        WHEN 'EU' THEN '欧洲'
        WHEN 'NA' THEN '北美洲'
        WHEN 'OC' THEN '大洋洲'
        WHEN 'SA' THEN '南美洲'
        WHEN 'AN' THEN '南极洲'
        ELSE ''
    END as continent_name,
    NVL(c.CAPITAL, '') as capital,
    NVL(c.CURRENCY_CODE, '') as currency_code,
    NVL(c.CURRENCY_NAME, '') as currency_name,
    NVL(c.LANGUAGES, '') as languages,
    
    -- 行政区信息（业务友好）
    l.ADMIN1_CODE as admin1_code,
    NVL(ac.NAME, l.ADMIN1_CODE) as admin1_name,
    l.ADMIN2_CODE as admin2_code,
    NVL(c.COUNTRY_NAME, l.COUNTRY_CODE) || 
    CASE WHEN ac.NAME IS NOT NULL THEN ' > ' || ac.NAME ELSE '' END as admin_full_path,
    
    -- 人口和海拔信息（业务友好）
    NVL(l.POPULATION, 0) as population,
    CASE 
        WHEN l.POPULATION >= 1000000 THEN ROUND(l.POPULATION/1000000, 1) || '百万'
        WHEN l.POPULATION >= 10000 THEN ROUND(l.POPULATION/10000, 1) || '万'
        WHEN l.POPULATION > 0 THEN TO_CHAR(l.POPULATION, 'FM999,999,999')
        ELSE ''
    END as population_formatted,
    l.ELEVATION as elevation,
    CASE WHEN l.ELEVATION IS NOT NULL THEN l.ELEVATION || '米' ELSE '' END as elevation_formatted,
    
    -- 时区信息（业务友好）
    l.TIMEZONE as timezone,
    NVL(tz.GMT_OFFSET, 0) as gmt_offset,
    CASE 
        WHEN tz.GMT_OFFSET = 0 THEN 'GMT'
        WHEN tz.GMT_OFFSET > 0 THEN 'GMT+' || tz.GMT_OFFSET
        ELSE 'GMT' || tz.GMT_OFFSET
    END as timezone_formatted,
    
    -- 业务标签和分类
    CASE WHEN l.FEATURE_CODE IN ('PPLC', 'PPLA', 'PPLA2') OR l.POPULATION > 100000 THEN 'Y' ELSE 'N' END as is_major_city,
    CASE WHEN l.FEATURE_CODE = 'PPLC' THEN 'Y' ELSE 'N' END as is_capital,
    CASE WHEN l.FEATURE_CODE IN ('HTL', 'MUS', 'TMPL', 'MNMT', 'AIRP', 'MTRO') THEN 'Y' ELSE 'N' END as is_tourist_attraction,
    
    -- 业务分类
    CASE 
        WHEN l.FEATURE_CLASS = 'P' AND l.FEATURE_CODE = 'PPLC' THEN '首都城市'
        WHEN l.FEATURE_CLASS = 'P' AND l.FEATURE_CODE IN ('PPLA', 'PPLA2') THEN '重要城市'
        WHEN l.FEATURE_CLASS = 'P' THEN '一般城市'
        WHEN l.FEATURE_CLASS = 'S' AND l.FEATURE_CODE = 'HTL' THEN '酒店住宿'
        WHEN l.FEATURE_CLASS = 'S' AND l.FEATURE_CODE = 'AIRP' THEN '交通枢纽'
        WHEN l.FEATURE_CLASS = 'S' THEN '服务设施'
        WHEN l.FEATURE_CLASS = 'T' THEN '自然景观'
        WHEN l.FEATURE_CLASS = 'H' THEN '水域地理'
        ELSE '其他地点'
    END as business_category,
    
    -- 重要性评分
    CASE 
        WHEN l.FEATURE_CODE = 'PPLC' THEN 100
        WHEN l.FEATURE_CODE IN ('PPLA', 'PPLA2') THEN 80
        WHEN l.FEATURE_CODE = 'AIRP' THEN 70
        WHEN l.POPULATION > 1000000 THEN 90
        WHEN l.POPULATION > 100000 THEN 60
        WHEN l.POPULATION > 10000 THEN 40
        ELSE 20
    END as importance_score,
    
    -- 元数据
    'GeoNames' as data_source,
    l.MODIFICATION_DATE as last_modified,
    l.CREATED_DATE as created_at

FROM GEONAMES_LOCATIONS l
LEFT JOIN GEONAMES_COUNTRIES c ON l.COUNTRY_CODE = c.ISO_CODE
LEFT JOIN GEONAMES_ADMIN_CODES ac ON l.COUNTRY_CODE = ac.COUNTRY_CODE 
    AND l.ADMIN1_CODE = ac.ADMIN_CODE AND ac.ADMIN_LEVEL = 1
LEFT JOIN GEONAMES_FEATURE_CODES fc ON l.FEATURE_CLASS = fc.FEATURE_CLASS 
    AND l.FEATURE_CODE = fc.FEATURE_CODE AND fc.LANGUAGE_CODE = 'en'
LEFT JOIN GEONAMES_TIMEZONES tz ON l.COUNTRY_CODE = tz.COUNTRY_CODE 
    AND l.TIMEZONE = tz.TIMEZONE_ID;

-- 添加视图注释
COMMENT ON VIEW V_BUSINESS_LOCATIONS IS '业务友好地理位置视图 - 提供用户友好的地理数据查询接口';

-- =====================================================================================
-- 2. 创建专门的业务查询视图
-- =====================================================================================

-- 主要城市视图
CREATE OR REPLACE VIEW V_MAJOR_CITIES AS
SELECT 
    geoname_id,
    name,
    country_name,
    country_name_cn,
    admin_full_path,
    population,
    population_formatted,
    coordinates,
    timezone_formatted,
    importance_score,
    business_category
FROM V_BUSINESS_LOCATIONS
WHERE is_major_city = 'Y'
ORDER BY importance_score DESC, population DESC;

-- 旅游景点视图
CREATE OR REPLACE VIEW V_TOURIST_ATTRACTIONS AS
SELECT 
    geoname_id,
    name,
    feature_name,
    feature_description,
    country_name,
    country_name_cn,
    admin_full_path,
    coordinates,
    business_category,
    importance_score
FROM V_BUSINESS_LOCATIONS
WHERE is_tourist_attraction = 'Y'
ORDER BY importance_score DESC;

-- 国家统计视图
CREATE OR REPLACE VIEW V_COUNTRY_BUSINESS_STATS AS
SELECT 
    country_code,
    country_name,
    country_name_cn,
    continent_name,
    currency_name,
    COUNT(*) as total_locations,
    COUNT(CASE WHEN is_major_city = 'Y' THEN 1 END) as major_cities_count,
    COUNT(CASE WHEN is_tourist_attraction = 'Y' THEN 1 END) as tourist_attractions_count,
    COUNT(CASE WHEN business_category = '交通枢纽' THEN 1 END) as transport_hubs_count,
    SUM(population) as total_population,
    ROUND(AVG(importance_score), 1) as avg_importance_score,
    MIN(latitude) as min_latitude,
    MAX(latitude) as max_latitude,
    MIN(longitude) as min_longitude,
    MAX(longitude) as max_longitude
FROM V_BUSINESS_LOCATIONS
GROUP BY country_code, country_name, country_name_cn, continent_name, currency_name
ORDER BY total_locations DESC;

-- =====================================================================================
-- 3. 创建业务查询存储过程
-- =====================================================================================

-- 地址搜索存储过程
CREATE OR REPLACE PROCEDURE SP_SEARCH_LOCATIONS(
    p_query IN VARCHAR2,
    p_country_code IN VARCHAR2 DEFAULT NULL,
    p_feature_category IN VARCHAR2 DEFAULT NULL,
    p_limit IN NUMBER DEFAULT 20,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
    SELECT 
        geoname_id,
        name,
        ascii_name,
        feature_name,
        country_name,
        admin_full_path,
        coordinates,
        population_formatted,
        business_category,
        importance_score
    FROM V_BUSINESS_LOCATIONS
    WHERE (p_query IS NULL OR 
           UPPER(name) LIKE '%' || UPPER(p_query) || '%' OR
           UPPER(ascii_name) LIKE '%' || UPPER(p_query) || '%')
      AND (p_country_code IS NULL OR country_code = p_country_code)
      AND (p_feature_category IS NULL OR feature_category = p_feature_category)
    ORDER BY importance_score DESC, population DESC
    FETCH FIRST p_limit ROWS ONLY;
END;
/

-- 反向地理编码存储过程
CREATE OR REPLACE PROCEDURE SP_REVERSE_GEOCODING(
    p_latitude IN NUMBER,
    p_longitude IN NUMBER,
    p_radius_km IN NUMBER DEFAULT 1,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
    SELECT 
        geoname_id,
        name,
        feature_name,
        country_name,
        admin_full_path,
        coordinates,
        business_category,
        importance_score,
        -- 计算距离（简化公式）
        ROUND(
            111.32 * SQRT(
                POWER(latitude - p_latitude, 2) + 
                POWER((longitude - p_longitude) * COS(RADIANS(p_latitude)), 2)
            ), 2
        ) as distance_km
    FROM V_BUSINESS_LOCATIONS
    WHERE ABS(latitude - p_latitude) <= p_radius_km / 111.32
      AND ABS(longitude - p_longitude) <= p_radius_km / (111.32 * COS(RADIANS(p_latitude)))
    ORDER BY distance_km, importance_score DESC
    FETCH FIRST 10 ROWS ONLY;
END;
/

-- 区域分析存储过程
CREATE OR REPLACE PROCEDURE SP_REGION_ANALYSIS(
    p_country_code IN VARCHAR2,
    p_business_category IN VARCHAR2 DEFAULT NULL,
    p_min_importance IN NUMBER DEFAULT 0,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN p_cursor FOR
    SELECT 
        business_category,
        COUNT(*) as location_count,
        ROUND(AVG(importance_score), 1) as avg_importance,
        ROUND(AVG(population), 0) as avg_population,
        COUNT(CASE WHEN is_major_city = 'Y' THEN 1 END) as major_cities,
        COUNT(CASE WHEN is_tourist_attraction = 'Y' THEN 1 END) as tourist_spots
    FROM V_BUSINESS_LOCATIONS
    WHERE country_code = p_country_code
      AND (p_business_category IS NULL OR business_category = p_business_category)
      AND importance_score >= p_min_importance
    GROUP BY business_category
    ORDER BY location_count DESC;
END;
/

-- =====================================================================================
-- 4. 创建业务友好的查询函数
-- =====================================================================================

-- 获取完整地址路径函数
CREATE OR REPLACE FUNCTION FN_GET_FULL_ADDRESS(
    p_geoname_id IN NUMBER
) RETURN VARCHAR2 AS
    v_address VARCHAR2(500);
BEGIN
    SELECT admin_full_path || ' > ' || name
    INTO v_address
    FROM V_BUSINESS_LOCATIONS
    WHERE geoname_id = p_geoname_id;
    
    RETURN v_address;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN NULL;
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
/

-- 计算两点距离函数
CREATE OR REPLACE FUNCTION FN_CALCULATE_DISTANCE(
    p_lat1 IN NUMBER,
    p_lon1 IN NUMBER,
    p_lat2 IN NUMBER,
    p_lon2 IN NUMBER
) RETURN NUMBER AS
    v_distance NUMBER;
BEGIN
    -- 使用简化的距离计算公式
    v_distance := 111.32 * SQRT(
        POWER(p_lat2 - p_lat1, 2) + 
        POWER((p_lon2 - p_lon1) * COS(RADIANS(p_lat1)), 2)
    );
    
    RETURN ROUND(v_distance, 2);
END;
/

-- 获取时区偏移函数
CREATE OR REPLACE FUNCTION FN_GET_TIMEZONE_OFFSET(
    p_country_code IN VARCHAR2,
    p_timezone IN VARCHAR2
) RETURN NUMBER AS
    v_offset NUMBER;
BEGIN
    SELECT gmt_offset
    INTO v_offset
    FROM GEONAMES_TIMEZONES
    WHERE country_code = p_country_code
      AND timezone_id = p_timezone;
    
    RETURN v_offset;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 0;
    WHEN OTHERS THEN
        RETURN 0;
END;
/

-- =====================================================================================
-- 5. 创建索引优化业务查询
-- =====================================================================================

-- 业务查询优化索引
CREATE INDEX IDX_BUSINESS_CATEGORY ON GEONAMES_LOCATIONS (
    CASE 
        WHEN FEATURE_CLASS = 'P' AND FEATURE_CODE = 'PPLC' THEN '首都城市'
        WHEN FEATURE_CLASS = 'P' AND FEATURE_CODE IN ('PPLA', 'PPLA2') THEN '重要城市'
        WHEN FEATURE_CLASS = 'P' THEN '一般城市'
        WHEN FEATURE_CLASS = 'S' AND FEATURE_CODE = 'HTL' THEN '酒店住宿'
        WHEN FEATURE_CLASS = 'S' AND FEATURE_CODE = 'AIRP' THEN '交通枢纽'
        WHEN FEATURE_CLASS = 'S' THEN '服务设施'
        WHEN FEATURE_CLASS = 'T' THEN '自然景观'
        WHEN FEATURE_CLASS = 'H' THEN '水域地理'
        ELSE '其他地点'
    END
);

-- 重要性评分索引
CREATE INDEX IDX_IMPORTANCE_SCORE ON GEONAMES_LOCATIONS (
    CASE 
        WHEN FEATURE_CODE = 'PPLC' THEN 100
        WHEN FEATURE_CODE IN ('PPLA', 'PPLA2') THEN 80
        WHEN FEATURE_CODE = 'AIRP' THEN 70
        WHEN POPULATION > 1000000 THEN 90
        WHEN POPULATION > 100000 THEN 60
        WHEN POPULATION > 10000 THEN 40
        ELSE 20
    END DESC
);

-- =====================================================================================
-- 输出创建完成信息
-- =====================================================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
    DBMS_OUTPUT.PUT_LINE('业务友好Oracle视图和存储过程创建完成！');
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
    DBMS_OUTPUT.PUT_LINE('已创建的对象:');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('📊 业务视图 (4个):');
    DBMS_OUTPUT.PUT_LINE('  1. V_BUSINESS_LOCATIONS - 主业务视图');
    DBMS_OUTPUT.PUT_LINE('  2. V_MAJOR_CITIES - 主要城市视图');
    DBMS_OUTPUT.PUT_LINE('  3. V_TOURIST_ATTRACTIONS - 旅游景点视图');
    DBMS_OUTPUT.PUT_LINE('  4. V_COUNTRY_BUSINESS_STATS - 国家统计视图');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('🔧 存储过程 (3个):');
    DBMS_OUTPUT.PUT_LINE('  1. SP_SEARCH_LOCATIONS - 地址搜索');
    DBMS_OUTPUT.PUT_LINE('  2. SP_REVERSE_GEOCODING - 反向地理编码');
    DBMS_OUTPUT.PUT_LINE('  3. SP_REGION_ANALYSIS - 区域分析');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('⚡ 业务函数 (3个):');
    DBMS_OUTPUT.PUT_LINE('  1. FN_GET_FULL_ADDRESS - 获取完整地址');
    DBMS_OUTPUT.PUT_LINE('  2. FN_CALCULATE_DISTANCE - 计算距离');
    DBMS_OUTPUT.PUT_LINE('  3. FN_GET_TIMEZONE_OFFSET - 获取时区偏移');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('📇 业务索引 (2个):');
    DBMS_OUTPUT.PUT_LINE('  1. IDX_BUSINESS_CATEGORY - 业务分类索引');
    DBMS_OUTPUT.PUT_LINE('  2. IDX_IMPORTANCE_SCORE - 重要性评分索引');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('🎯 使用示例:');
    DBMS_OUTPUT.PUT_LINE('  - SELECT * FROM V_BUSINESS_LOCATIONS WHERE country_code = ''CN'';');
    DBMS_OUTPUT.PUT_LINE('  - SELECT * FROM V_MAJOR_CITIES WHERE country_name_cn = ''中国'';');
    DBMS_OUTPUT.PUT_LINE('  - EXEC SP_SEARCH_LOCATIONS(''香港'', ''HK'', NULL, 10, :cursor);');
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
END;
/
