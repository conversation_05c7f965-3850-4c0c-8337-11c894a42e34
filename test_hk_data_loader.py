#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HK数据测试入库程序
专门用于测试HK.txt数据的入库，包含详细的错误诊断
"""

import cx_Oracle
import pandas as pd
import numpy as np
from datetime import datetime
import os
import traceback

class HKDataLoader:
    """HK数据加载器"""
    
    def __init__(self):
        # 使用您提供的连接字符串
        self.connection_string = "manifest_dcb/manifest_dcb@192.168.1.151/TEST"
        self.connection = None
        self.cursor = None
        
        # 辅助数据
        self.feature_codes = {}
        self.admin_codes = {}
        self.country_info = {}
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = cx_Oracle.connect(self.connection_string)
            self.cursor = self.connection.cursor()
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("✅ 数据库连接已关闭")
    
    def load_auxiliary_data(self):
        """加载辅助数据"""
        print("🔄 加载辅助数据...")
        
        # 1. 加载特征代码
        try:
            with open('featureCodes_en.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        self.feature_codes[parts[0]] = {
                            'name': parts[1] if len(parts) > 1 else '',
                            'description': parts[2] if len(parts) > 2 else ''
                        }
            print(f"✅ 特征代码: {len(self.feature_codes)} 个")
        except Exception as e:
            print(f"⚠️ 特征代码加载失败: {e}")
        
        # 2. 加载行政区代码
        try:
            with open('admin1CodesASCII.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        self.admin_codes[parts[0]] = parts[1]
            print(f"✅ 行政区代码: {len(self.admin_codes)} 个")
        except Exception as e:
            print(f"⚠️ 行政区代码加载失败: {e}")
        
        # 3. 加载国家信息
        try:
            with open('countryInfo.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    if not line.startswith('#') and line.strip():
                        parts = line.strip().split('\t')
                        if len(parts) >= 16:
                            self.country_info[parts[0]] = {
                                'name': parts[4],
                                'continent': parts[8],
                                'currency_code': parts[10],
                                'currency_name': parts[11],
                                'languages': parts[15],
                                'capital': parts[5]
                            }
            print(f"✅ 国家信息: {len(self.country_info)} 个")
        except Exception as e:
            print(f"⚠️ 国家信息加载失败: {e}")
    
    def safe_str(self, value, max_length=None):
        """安全转换为字符串"""
        if pd.isna(value) or value is None:
            return None
        result = str(value)
        if max_length and len(result) > max_length:
            result = result[:max_length]
        return result
    
    def safe_number(self, value):
        """安全转换为数字"""
        if pd.isna(value) or value is None:
            return None
        try:
            return float(value)
        except:
            return None
    
    def test_table_structure(self):
        """测试表结构"""
        print("🔍 检查表结构...")
        
        try:
            # 检查业务友好表结构
            self.cursor.execute("""
                SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
                FROM USER_TAB_COLUMNS 
                WHERE TABLE_NAME = 'GEONAMES_BUSINESS_FRIENDLY'
                ORDER BY COLUMN_ID
            """)
            
            columns = self.cursor.fetchall()
            print(f"✅ GEONAMES_BUSINESS_FRIENDLY 表有 {len(columns)} 个字段:")
            
            for i, (col_name, data_type, data_length, nullable) in enumerate(columns, 1):
                print(f"  {i:2d}. {col_name:25s} {data_type}({data_length}) {'NULL' if nullable == 'Y' else 'NOT NULL'}")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查表结构失败: {e}")
            return False
    
    def test_single_record(self):
        """测试插入单条记录"""
        print("\n🧪 测试插入单条记录...")
        
        try:
            # 读取HK.txt的第一条记录
            columns = [
                'geonameid', 'name', 'asciiname', 'alternatenames',
                'latitude', 'longitude', 'feature_class', 'feature_code',
                'country_code', 'cc2', 'admin1_code', 'admin2_code',
                'admin3_code', 'admin4_code', 'population', 'elevation',
                'dem', 'timezone', 'modification_date'
            ]
            
            df = pd.read_csv('HK.txt', sep='\t', names=columns, encoding='utf-8', nrows=1)
            row = df.iloc[0]
            
            print(f"测试记录: {row['name']} (ID: {row['geonameid']})")
            
            # 获取辅助信息
            country_info = self.country_info.get('HK', {})
            feature_info = self.feature_codes.get(f"{row['feature_class']}.{row['feature_code']}", {})
            admin1_name = self.admin_codes.get(f"HK.{row['admin1_code']}", '')
            
            # 处理日期
            mod_date = None
            if pd.notna(row['modification_date']):
                try:
                    mod_date = datetime.strptime(str(row['modification_date']), '%Y-%m-%d')
                except:
                    mod_date = None
            
            # 计算业务字段
            population = self.safe_number(row['population'])
            importance_score = 20
            if row['feature_code'] == 'PPLC':
                importance_score = 100
            elif row['feature_code'] in ['PPLA', 'PPLA2']:
                importance_score = 80
            
            if population and population > 1000000:
                importance_score += 30
            elif population and population > 100000:
                importance_score += 20
            
            importance_score = min(importance_score, 100)
            
            # 准备插入SQL
            insert_sql = """
            INSERT INTO GEONAMES_BUSINESS_FRIENDLY (
                GEONAME_ID, NAME, ASCII_NAME, ALTERNATE_NAMES,
                LATITUDE, LONGITUDE, FEATURE_CLASS, FEATURE_CODE,
                FEATURE_NAME, FEATURE_NAME_CN, FEATURE_CATEGORY, FEATURE_DESCRIPTION,
                COUNTRY_CODE, COUNTRY_NAME, COUNTRY_NAME_CN, CONTINENT_CODE, CONTINENT_NAME,
                CAPITAL, CURRENCY_CODE, CURRENCY_NAME, LANGUAGES,
                ADMIN1_CODE, ADMIN1_NAME, ADMIN2_CODE, ADMIN2_NAME, ADMIN_FULL_PATH,
                POPULATION, POPULATION_FORMATTED, POPULATION_LEVEL,
                ELEVATION, ELEVATION_FORMATTED, ELEVATION_LEVEL,
                TIMEZONE, GMT_OFFSET, TIMEZONE_FORMATTED,
                BUSINESS_CATEGORY, IMPORTANCE_SCORE,
                IS_MAJOR_CITY, IS_CAPITAL, IS_TOURIST_ATTRACTION, IS_TRANSPORT_HUB,
                CC2, ADMIN3_CODE, ADMIN4_CODE, DEM, MODIFICATION_DATE
            ) VALUES (
                :1, :2, :3, :4, :5, :6, :7, :8, :9, :10,
                :11, :12, :13, :14, :15, :16, :17, :18, :19, :20,
                :21, :22, :23, :24, :25, :26, :27, :28, :29, :30,
                :31, :32, :33, :34, :35, :36, :37, :38, :39, :40,
                :41, :42, :43, :44, :45, :46
            )
            """
            
            # 准备数据
            record = (
                self.safe_number(row['geonameid']),                    # 1
                self.safe_str(row['name'], 200),                       # 2
                self.safe_str(row['asciiname'], 200),                  # 3
                self.safe_str(row['alternatenames']),                  # 4
                self.safe_number(row['latitude']),                     # 5
                self.safe_number(row['longitude']),                    # 6
                self.safe_str(row['feature_class'], 1),                # 7
                self.safe_str(row['feature_code'], 10),                # 8
                feature_info.get('name', ''),                          # 9
                '居住地' if row['feature_class'] == 'P' else '其他',    # 10
                '居住地点' if row['feature_class'] == 'P' else '其他',  # 11
                feature_info.get('description', ''),                   # 12
                'HK',                                                   # 13
                'Hong Kong',                                            # 14
                '香港',                                                 # 15
                'AS',                                                   # 16
                '亚洲',                                                 # 17
                'Hong Kong',                                            # 18
                'HKD',                                                  # 19
                'Dollar',                                               # 20
                'zh-HK,yue,zh,en',                                     # 21
                self.safe_str(row['admin1_code'], 20),                 # 22
                admin1_name,                                            # 23
                self.safe_str(row['admin2_code'], 80),                 # 24
                '',                                                     # 25 ADMIN2_NAME
                f"Hong Kong{' > ' + admin1_name if admin1_name else ''}", # 26
                population,                                             # 27
                f"{population/10000:.1f}万" if population and population > 10000 else str(int(population)) if population else '', # 28
                '大城市' if population and population > 1000000 else '中等城市' if population and population > 100000 else '', # 29
                self.safe_number(row['elevation']),                    # 30
                f"{int(row['elevation'])}米" if pd.notna(row['elevation']) else '', # 31
                '平原' if pd.notna(row['elevation']) and row['elevation'] < 1000 else '', # 32
                self.safe_str(row['timezone'], 40),                   # 33
                8.0,                                                    # 34 GMT_OFFSET
                'GMT+8',                                                # 35
                '首都城市' if row['feature_code'] == 'PPLC' else '重要城市' if row['feature_code'] in ['PPLA', 'PPLA2'] else '一般城市', # 36
                importance_score,                                       # 37
                'Y' if (row['feature_code'] in ['PPLC', 'PPLA', 'PPLA2'] or (population and population > 100000)) else 'N', # 38
                'Y' if row['feature_code'] == 'PPLC' else 'N',        # 39
                'N',                                                    # 40
                'N',                                                    # 41
                self.safe_str(row['cc2'], 200),                        # 42
                self.safe_str(row['admin3_code'], 20),                 # 43
                self.safe_str(row['admin4_code'], 20),                 # 44
                self.safe_number(row['dem']),                          # 45
                mod_date                                                # 46
            )
            
            print(f"准备插入数据，共 {len(record)} 个字段")
            
            # 执行插入
            self.cursor.execute(insert_sql, record)
            self.connection.commit()
            
            print("✅ 单条记录插入成功！")
            return True
            
        except Exception as e:
            print(f"❌ 单条记录插入失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            self.connection.rollback()
            return False
    
    def load_hk_data(self, batch_size=100):
        """加载HK数据"""
        print(f"\n🔄 开始加载HK.txt数据 (批次大小: {batch_size})")
        
        # 定义列名
        columns = [
            'geonameid', 'name', 'asciiname', 'alternatenames',
            'latitude', 'longitude', 'feature_class', 'feature_code',
            'country_code', 'cc2', 'admin1_code', 'admin2_code',
            'admin3_code', 'admin4_code', 'population', 'elevation',
            'dem', 'timezone', 'modification_date'
        ]
        
        total_processed = 0
        error_count = 0
        
        try:
            # 分块读取文件
            for chunk_num, chunk in enumerate(pd.read_csv('HK.txt', sep='\t', names=columns, 
                                           encoding='utf-8', chunksize=batch_size, low_memory=False), 1):
                
                print(f"处理第 {chunk_num} 块数据，共 {len(chunk)} 条记录...")
                
                batch_data = []
                for _, row in chunk.iterrows():
                    try:
                        # 获取辅助信息
                        country_info = self.country_info.get('HK', {})
                        feature_info = self.feature_codes.get(f"{row['feature_class']}.{row['feature_code']}", {})
                        admin1_name = self.admin_codes.get(f"HK.{row['admin1_code']}", '')
                        
                        # 处理日期
                        mod_date = None
                        if pd.notna(row['modification_date']):
                            try:
                                mod_date = datetime.strptime(str(row['modification_date']), '%Y-%m-%d')
                            except:
                                mod_date = None
                        
                        # 计算业务字段
                        population = self.safe_number(row['population'])
                        importance_score = 20
                        if row['feature_code'] == 'PPLC':
                            importance_score = 100
                        elif row['feature_code'] in ['PPLA', 'PPLA2']:
                            importance_score = 80
                        
                        if population and population > 1000000:
                            importance_score += 30
                        elif population and population > 100000:
                            importance_score += 20
                        
                        importance_score = min(importance_score, 100)
                        
                        # 准备数据 (简化版本，只包含必要字段)
                        record = (
                            self.safe_number(row['geonameid']),
                            self.safe_str(row['name'], 200),
                            self.safe_str(row['asciiname'], 200),
                            self.safe_str(row['alternatenames']),
                            self.safe_number(row['latitude']),
                            self.safe_number(row['longitude']),
                            self.safe_str(row['feature_class'], 1),
                            self.safe_str(row['feature_code'], 10),
                            feature_info.get('name', ''),
                            '居住地' if row['feature_class'] == 'P' else '其他',
                            '居住地点' if row['feature_class'] == 'P' else '其他',
                            feature_info.get('description', ''),
                            'HK',
                            'Hong Kong',
                            '香港',
                            'AS',
                            '亚洲',
                            'Hong Kong',
                            'HKD',
                            'Dollar',
                            'zh-HK,yue,zh,en',
                            self.safe_str(row['admin1_code'], 20),
                            admin1_name,
                            self.safe_str(row['admin2_code'], 80),
                            '',  # ADMIN2_NAME
                            f"Hong Kong{' > ' + admin1_name if admin1_name else ''}",
                            population,
                            f"{population/10000:.1f}万" if population and population > 10000 else str(int(population)) if population else '',
                            '大城市' if population and population > 1000000 else '中等城市' if population and population > 100000 else '',
                            self.safe_number(row['elevation']),
                            f"{int(row['elevation'])}米" if pd.notna(row['elevation']) else '',
                            '平原' if pd.notna(row['elevation']) and row['elevation'] < 1000 else '',
                            self.safe_str(row['timezone'], 40),
                            8.0,
                            'GMT+8',
                            '首都城市' if row['feature_code'] == 'PPLC' else '重要城市' if row['feature_code'] in ['PPLA', 'PPLA2'] else '一般城市',
                            importance_score,
                            'Y' if (row['feature_code'] in ['PPLC', 'PPLA', 'PPLA2'] or (population and population > 100000)) else 'N',
                            'Y' if row['feature_code'] == 'PPLC' else 'N',
                            'N',
                            'N',
                            self.safe_str(row['cc2'], 200),
                            self.safe_str(row['admin3_code'], 20),
                            self.safe_str(row['admin4_code'], 20),
                            self.safe_number(row['dem']),
                            mod_date
                        )
                        batch_data.append(record)
                        
                    except Exception as e:
                        error_count += 1
                        if error_count <= 5:
                            print(f"⚠️ 处理记录 {row['geonameid']} 出错: {e}")
                        continue
                
                # 批量插入
                if batch_data:
                    try:
                        insert_sql = """
                        INSERT INTO GEONAMES_BUSINESS_FRIENDLY (
                            GEONAME_ID, NAME, ASCII_NAME, ALTERNATE_NAMES,
                            LATITUDE, LONGITUDE, FEATURE_CLASS, FEATURE_CODE,
                            FEATURE_NAME, FEATURE_NAME_CN, FEATURE_CATEGORY, FEATURE_DESCRIPTION,
                            COUNTRY_CODE, COUNTRY_NAME, COUNTRY_NAME_CN, CONTINENT_CODE, CONTINENT_NAME,
                            CAPITAL, CURRENCY_CODE, CURRENCY_NAME, LANGUAGES,
                            ADMIN1_CODE, ADMIN1_NAME, ADMIN2_CODE, ADMIN2_NAME, ADMIN_FULL_PATH,
                            POPULATION, POPULATION_FORMATTED, POPULATION_LEVEL,
                            ELEVATION, ELEVATION_FORMATTED, ELEVATION_LEVEL,
                            TIMEZONE, GMT_OFFSET, TIMEZONE_FORMATTED,
                            BUSINESS_CATEGORY, IMPORTANCE_SCORE,
                            IS_MAJOR_CITY, IS_CAPITAL, IS_TOURIST_ATTRACTION, IS_TRANSPORT_HUB,
                            CC2, ADMIN3_CODE, ADMIN4_CODE, DEM, MODIFICATION_DATE
                        ) VALUES (
                            :1, :2, :3, :4, :5, :6, :7, :8, :9, :10,
                            :11, :12, :13, :14, :15, :16, :17, :18, :19, :20,
                            :21, :22, :23, :24, :25, :26, :27, :28, :29, :30,
                            :31, :32, :33, :34, :35, :36, :37, :38, :39, :40,
                            :41, :42, :43, :44, :45, :46
                        )
                        """
                        
                        self.cursor.executemany(insert_sql, batch_data)
                        self.connection.commit()
                        total_processed += len(batch_data)
                        print(f"✅ 第 {chunk_num} 块处理完成，累计 {total_processed:,} 条记录")
                        
                    except Exception as e:
                        print(f"❌ 第 {chunk_num} 块批量插入失败: {e}")
                        print(f"详细错误: {traceback.format_exc()}")
                        self.connection.rollback()
                        break
        
        except Exception as e:
            print(f"❌ 加载HK数据失败: {e}")
            print(f"详细错误: {traceback.format_exc()}")
            return False
        
        print(f"🎉 HK数据加载完成: 成功 {total_processed:,} 条，错误 {error_count} 条")
        return True
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始HK数据入库测试")
        print("=" * 60)
        
        # 连接数据库
        if not self.connect():
            return False
        
        try:
            # 加载辅助数据
            self.load_auxiliary_data()
            
            # 检查表结构
            if not self.test_table_structure():
                return False
            
            # 测试单条记录
            if not self.test_single_record():
                return False
            
            # 询问是否继续加载全部数据
            user_input = input("\n单条记录测试成功！是否继续加载全部HK数据？(y/n): ").strip().lower()
            if user_input == 'y':
                return self.load_hk_data()
            else:
                print("测试完成，未加载全部数据")
                return True
                
        finally:
            self.disconnect()

def main():
    """主函数"""
    loader = HKDataLoader()
    loader.run_test()

if __name__ == "__main__":
    main()
