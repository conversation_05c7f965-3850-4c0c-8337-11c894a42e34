#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeoNames辅助数据关联分析脚本
分析辅助数据文件与核心地理数据的关联关系
"""

import pandas as pd
import numpy as np
from collections import defaultdict

def analyze_auxiliary_data_relations():
    """分析辅助数据与核心数据的关联关系"""
    
    print("=" * 80)
    print("GeoNames辅助数据关联分析报告")
    print("=" * 80)
    
    # 1. 读取核心数据 (香港数据作为示例)
    print("\n1. 读取核心地理数据 (HK.txt)")
    print("-" * 50)
    
    columns = [
        'geonameid', 'name', 'asciiname', 'alternatenames',
        'latitude', 'longitude', 'feature_class', 'feature_code',
        'country_code', 'cc2', 'admin1_code', 'admin2_code',
        'admin3_code', 'admin4_code', 'population', 'elevation',
        'dem', 'timezone', 'modification_date'
    ]
    
    try:
        hk_data = pd.read_csv('HK.txt', sep='\t', names=columns, encoding='utf-8')
        print(f"✅ 成功读取香港数据: {len(hk_data)} 条记录")
        
        # 显示几个示例记录
        print("\n核心数据示例:")
        for i in range(min(3, len(hk_data))):
            row = hk_data.iloc[i]
            print(f"  记录 {i+1}:")
            print(f"    ID: {row['geonameid']}")
            print(f"    名称: {row['name']}")
            print(f"    特征: {row['feature_class']}.{row['feature_code']}")
            print(f"    行政区: {row['admin1_code']}")
            print(f"    时区: {row['timezone']}")
            print()
            
    except Exception as e:
        print(f"❌ 读取香港数据失败: {e}")
        return
    
    # 2. 分析行政区代码关联
    print("2. 行政区代码关联分析 (admin1CodesASCII.txt)")
    print("-" * 50)
    
    try:
        admin1_data = pd.read_csv('admin1CodesASCII.txt', sep='\t', 
                                 names=['code', 'name', 'asciiname', 'geonameid'],
                                 encoding='utf-8')
        
        # 筛选香港的行政区
        hk_admin1 = admin1_data[admin1_data['code'].str.startswith('HK.')]
        print(f"✅ 香港一级行政区数量: {len(hk_admin1)}")
        
        print("\n香港行政区列表:")
        for _, row in hk_admin1.iterrows():
            admin_code = row['code'].split('.')[1]  # 提取HK.后面的部分
            print(f"  {admin_code}: {row['name']} (GeoNameID: {row['geonameid']})")
        
        # 分析核心数据中的行政区使用情况
        print(f"\n核心数据中的行政区代码使用情况:")
        admin1_usage = hk_data['admin1_code'].value_counts()
        for code, count in admin1_usage.items():
            if pd.notna(code) and code != '':
                admin_name = hk_admin1[hk_admin1['code'] == f'HK.{code}']['name'].iloc[0] if len(hk_admin1[hk_admin1['code'] == f'HK.{code}']) > 0 else '未知'
                print(f"  {code}: {count} 个地点 - {admin_name}")
        
    except Exception as e:
        print(f"❌ 读取行政区数据失败: {e}")
    
    # 3. 分析国家信息关联
    print("\n3. 国家信息关联分析 (countryInfo.txt)")
    print("-" * 50)
    
    try:
        # 读取国家信息，跳过注释行
        country_data = []
        with open('countryInfo.txt', 'r', encoding='utf-8') as f:
            for line in f:
                if not line.startswith('#') and line.strip():
                    country_data.append(line.strip().split('\t'))
        
        country_columns = [
            'iso', 'iso3', 'iso_numeric', 'fips', 'country', 'capital',
            'area', 'population', 'continent', 'tld', 'currency_code',
            'currency_name', 'phone', 'postal_code_format', 'postal_code_regex',
            'languages', 'geonameid', 'neighbours'
        ]
        
        country_df = pd.DataFrame(country_data, columns=country_columns[:len(country_data[0])])
        
        # 查找香港信息
        hk_country = country_df[country_df['iso'] == 'HK']
        if len(hk_country) > 0:
            hk_info = hk_country.iloc[0]
            print("✅ 香港国家信息:")
            print(f"  国家代码: {hk_info['iso']} ({hk_info['iso3']})")
            print(f"  名称: {hk_info['country']}")
            print(f"  首都: {hk_info['capital']}")
            print(f"  面积: {hk_info['area']} 平方公里")
            print(f"  人口: {hk_info['population']}")
            print(f"  大洲: {hk_info['continent']}")
            print(f"  货币: {hk_info['currency_name']} ({hk_info['currency_code']})")
            print(f"  语言: {hk_info['languages']}")
            print(f"  GeoNameID: {hk_info['geonameid']}")
        
    except Exception as e:
        print(f"❌ 读取国家信息失败: {e}")
    
    # 4. 分析特征代码关联
    print("\n4. 特征代码关联分析 (featureCodes_en.txt)")
    print("-" * 50)
    
    try:
        feature_data = pd.read_csv('featureCodes_en.txt', sep='\t',
                                  names=['code', 'name', 'description'],
                                  encoding='utf-8')
        
        # 分析香港数据中使用的特征代码
        hk_features = hk_data['feature_class'].str.cat(hk_data['feature_code'], sep='.')
        feature_counts = hk_features.value_counts().head(10)
        
        print("✅ 香港数据中前10个特征代码:")
        for feature_code, count in feature_counts.items():
            feature_info = feature_data[feature_data['code'] == feature_code]
            if len(feature_info) > 0:
                name = feature_info.iloc[0]['name']
                desc = feature_info.iloc[0]['description']
                print(f"  {feature_code}: {count} 个 - {name}")
                print(f"    说明: {desc}")
            else:
                print(f"  {feature_code}: {count} 个 - 未找到说明")
            print()
        
    except Exception as e:
        print(f"❌ 读取特征代码失败: {e}")
    
    # 5. 分析时区信息关联
    print("5. 时区信息关联分析 (timeZones.txt)")
    print("-" * 50)
    
    try:
        timezone_data = pd.read_csv('timeZones.txt', sep='\t',
                                   names=['country_code', 'timezone_id', 'gmt_offset', 'dst_offset', 'raw_offset'],
                                   encoding='utf-8')
        
        # 查找香港时区信息
        hk_timezone = timezone_data[timezone_data['country_code'] == 'HK']
        if len(hk_timezone) > 0:
            tz_info = hk_timezone.iloc[0]
            print("✅ 香港时区信息:")
            print(f"  时区ID: {tz_info['timezone_id']}")
            print(f"  GMT偏移: {tz_info['gmt_offset']} 小时")
            print(f"  夏令时偏移: {tz_info['dst_offset']} 小时")
            print(f"  原始偏移: {tz_info['raw_offset']} 小时")
        
        # 检查核心数据中的时区使用情况
        timezone_usage = hk_data['timezone'].value_counts()
        print(f"\n核心数据中的时区使用:")
        for tz, count in timezone_usage.items():
            print(f"  {tz}: {count} 个地点")
        
    except Exception as e:
        print(f"❌ 读取时区信息失败: {e}")
    
    # 6. 数据关联总结
    print("\n6. 数据关联总结和应用建议")
    print("-" * 50)
    
    print("✅ 辅助数据的关联价值:")
    print()
    print("🔗 admin1CodesASCII.txt - 行政区代码对照")
    print("  - 将admin1_code转换为可读的行政区名称")
    print("  - 支持多语言行政区名称显示")
    print("  - 可以构建完整的行政区层级结构")
    print()
    
    print("🔗 countryInfo.txt - 国家详细信息")
    print("  - 提供国家的完整背景信息")
    print("  - 包含面积、人口、货币、语言等")
    print("  - 支持国际化应用开发")
    print()
    
    print("🔗 featureCodes_en.txt - 特征代码说明")
    print("  - 将抽象的特征代码转换为可理解的描述")
    print("  - 支持用户界面的友好显示")
    print("  - 便于数据分类和筛选")
    print()
    
    print("🔗 timeZones.txt - 时区详细信息")
    print("  - 提供精确的时区偏移量")
    print("  - 支持夏令时计算")
    print("  - 便于全球化应用的时间处理")
    print()
    
    print("💡 实际应用示例:")
    print("  1. 地址显示: '香港 荃湾区 荃湾' 而不是 'HK NTW'")
    print("  2. 特征说明: '一级行政中心' 而不是 'PPLA'")
    print("  3. 时间转换: 自动处理GMT+8时区转换")
    print("  4. 国际化: 支持多语言界面和货币显示")
    
    # 7. 生成关联查询示例
    print("\n7. SQL关联查询示例")
    print("-" * 50)
    
    sql_examples = """
-- 查询香港所有地点及其行政区名称
SELECT 
    l.name AS location_name,
    l.feature_class || '.' || l.feature_code AS feature,
    fc.name AS feature_description,
    a.name AS admin1_name,
    l.population,
    l.latitude,
    l.longitude
FROM geonames_locations l
LEFT JOIN geonames_admin_codes a ON l.country_code = a.country_code 
    AND l.admin1_code = a.admin_code AND a.admin_level = 1
LEFT JOIN geonames_feature_codes fc ON l.feature_class = fc.feature_class 
    AND l.feature_code = fc.feature_code
WHERE l.country_code = 'HK'
ORDER BY l.population DESC NULLS LAST;

-- 查询包含完整国家信息的地点
SELECT 
    l.name AS location_name,
    c.country_name,
    c.capital,
    c.currency_name,
    c.languages,
    l.timezone,
    tz.gmt_offset
FROM geonames_locations l
LEFT JOIN geonames_countries c ON l.country_code = c.iso_code
LEFT JOIN geonames_timezones tz ON l.country_code = tz.country_code 
    AND l.timezone = tz.timezone_id
WHERE l.country_code = 'HK' AND l.population > 100000;
"""
    
    print(sql_examples)
    
    print("=" * 80)
    print("分析完成！辅助数据为核心地理数据提供了丰富的关联信息。")
    print("=" * 80)

if __name__ == "__main__":
    analyze_auxiliary_data_relations()
