-- =====================================================================================
-- GeoNames完整数据库表创建脚本 (Oracle 11g)
-- 创建日期: 2025-08-07
-- 描述: 用于存储GeoNames完整数据的Oracle数据库表结构
-- 包含: 主表、别名表、行政区表、国家表、特征代码表、时区表等
-- 数据来源: https://download.geonames.org/export/dump/
-- 许可证: Creative Commons Attribution 4.0
-- =====================================================================================

-- 删除已存在的表（如果存在）
BEGIN
    FOR c IN (SELECT table_name FROM user_tables WHERE table_name LIKE 'GEONAMES_%') LOOP
        EXECUTE IMMEDIATE 'DROP TABLE ' || c.table_name || ' CASCADE CONSTRAINTS';
        DBMS_OUTPUT.PUT_LINE('已删除表: ' || c.table_name);
    END LOOP;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('清理表时出现错误: ' || SQLERRM);
END;
/

-- 删除已存在的序列（如果存在）
BEGIN
    FOR c IN (SELECT sequence_name FROM user_sequences WHERE sequence_name LIKE 'SEQ_GEONAMES_%') LOOP
        EXECUTE IMMEDIATE 'DROP SEQUENCE ' || c.sequence_name;
        DBMS_OUTPUT.PUT_LINE('已删除序列: ' || c.sequence_name);
    END LOOP;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('清理序列时出现错误: ' || SQLERRM);
END;
/

-- =====================================================================================
-- 1. 创建主表：GEONAMES_LOCATIONS
-- 描述: 存储全球地理位置的详细信息
-- =====================================================================================

CREATE TABLE GEONAMES_LOCATIONS (
    -- 主键和标识字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID（自增）
    GEONAME_ID          NUMBER(12)      NOT NULL,           -- GeoNames官方ID（来自数据源）
    
    -- 地名信息字段
    NAME                NVARCHAR2(200)  NOT NULL,           -- 地理位置名称（UTF-8，支持多语言）
    ASCII_NAME          VARCHAR2(200),                      -- ASCII格式的地名（英文字符）
    ALTERNATE_NAMES     NCLOB,                              -- 别名列表（逗号分隔，包含多语言名称）
    
    -- 地理坐标字段
    LATITUDE            NUMBER(10,7)    NOT NULL,           -- 纬度（WGS84坐标系，精度到小数点后7位）
    LONGITUDE           NUMBER(10,7)    NOT NULL,           -- 经度（WGS84坐标系，精度到小数点后7位）
    
    -- 地理特征分类字段
    FEATURE_CLASS       CHAR(1)         NOT NULL,           -- 特征类别（A/H/L/P/R/S/T/U/V）
    FEATURE_CODE        VARCHAR2(10)    NOT NULL,           -- 特征代码（详细分类，如PPL/MT/HTL等）
    
    -- 行政区划字段
    COUNTRY_CODE        CHAR(2)         NOT NULL,           -- 国家代码（ISO-3166 2位字母代码）
    CC2                 VARCHAR2(200),                      -- 备用国家代码（逗号分隔）
    ADMIN1_CODE         VARCHAR2(20),                       -- 一级行政区代码（省/州级）
    ADMIN2_CODE         VARCHAR2(80),                       -- 二级行政区代码（市/县级）
    ADMIN3_CODE         VARCHAR2(20),                       -- 三级行政区代码（区/镇级）
    ADMIN4_CODE         VARCHAR2(20),                       -- 四级行政区代码（街道/村级）
    
    -- 人口和海拔信息
    POPULATION          NUMBER(12),                         -- 人口数量（可为空）
    ELEVATION           NUMBER(6),                          -- 海拔高度（米，可为负值）
    DEM                 NUMBER(6),                          -- 数字高程模型（SRTM3或GTOPO30）
    
    -- 时区和时间信息
    TIMEZONE            VARCHAR2(40),                       -- IANA时区标识符（如Asia/Shanghai）
    MODIFICATION_DATE   DATE,                               -- 最后修改日期
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    DATA_SOURCE         VARCHAR2(50)    DEFAULT 'GEONAMES', -- 数据来源标识
    
    -- 主键约束
    CONSTRAINT PK_GEONAMES_LOCATIONS PRIMARY KEY (ID),
    
    -- 唯一约束
    CONSTRAINT UK_GEONAMES_ID UNIQUE (GEONAME_ID),
    
    -- 检查约束
    CONSTRAINT CK_LATITUDE CHECK (LATITUDE BETWEEN -90 AND 90),
    CONSTRAINT CK_LONGITUDE CHECK (LONGITUDE BETWEEN -180 AND 180),
    CONSTRAINT CK_FEATURE_CLASS CHECK (FEATURE_CLASS IN ('A','H','L','P','R','S','T','U','V')),
    CONSTRAINT CK_POPULATION CHECK (POPULATION >= 0),
    CONSTRAINT CK_COUNTRY_CODE CHECK (LENGTH(COUNTRY_CODE) = 2)
);

-- =====================================================================================
-- 2. 创建辅助表：GEONAMES_ALTERNATE_NAMES
-- 描述: 存储地理位置的多语言别名信息
-- =====================================================================================

CREATE TABLE GEONAMES_ALTERNATE_NAMES (
    -- 主键和关联字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID
    ALTERNATE_NAME_ID   NUMBER(12)      NOT NULL,           -- 别名ID（来自数据源）
    GEONAME_ID          NUMBER(12)      NOT NULL,           -- 关联的地理位置ID
    
    -- 语言和名称信息
    ISO_LANGUAGE        VARCHAR2(7),                        -- ISO语言代码
    ALTERNATE_NAME      NVARCHAR2(400)  NOT NULL,           -- 别名或名称变体
    
    -- 名称属性标志
    IS_PREFERRED_NAME   CHAR(1)         DEFAULT '0',        -- 是否为首选名称 (1/0)
    IS_SHORT_NAME       CHAR(1)         DEFAULT '0',        -- 是否为简称 (1/0)
    IS_COLLOQUIAL       CHAR(1)         DEFAULT '0',        -- 是否为俗称 (1/0)
    IS_HISTORIC         CHAR(1)         DEFAULT '0',        -- 是否为历史名称 (1/0)
    
    -- 使用时期
    FROM_PERIOD         VARCHAR2(20),                       -- 开始使用时期
    TO_PERIOD           VARCHAR2(20),                       -- 结束使用时期
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    
    -- 约束
    CONSTRAINT PK_ALTERNATE_NAMES PRIMARY KEY (ID),
    CONSTRAINT UK_ALTERNATE_NAME_ID UNIQUE (ALTERNATE_NAME_ID),
    CONSTRAINT FK_ALTERNATE_GEONAME FOREIGN KEY (GEONAME_ID) REFERENCES GEONAMES_LOCATIONS(GEONAME_ID),
    CONSTRAINT CK_IS_PREFERRED CHECK (IS_PREFERRED_NAME IN ('0','1')),
    CONSTRAINT CK_IS_SHORT CHECK (IS_SHORT_NAME IN ('0','1')),
    CONSTRAINT CK_IS_COLLOQUIAL CHECK (IS_COLLOQUIAL IN ('0','1')),
    CONSTRAINT CK_IS_HISTORIC CHECK (IS_HISTORIC IN ('0','1'))
);

-- =====================================================================================
-- 3. 创建辅助表：GEONAMES_ADMIN_CODES
-- 描述: 存储行政区划代码对照信息
-- =====================================================================================

CREATE TABLE GEONAMES_ADMIN_CODES (
    -- 主键和标识字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID
    COUNTRY_CODE        CHAR(2)         NOT NULL,           -- 国家代码
    ADMIN_LEVEL         NUMBER(1)       NOT NULL,           -- 行政级别 (1-5)
    ADMIN_CODE          VARCHAR2(20)    NOT NULL,           -- 行政区代码
    
    -- 名称信息
    NAME                NVARCHAR2(200)  NOT NULL,           -- 行政区名称
    ASCII_NAME          VARCHAR2(200),                      -- ASCII名称
    GEONAME_ID          NUMBER(12),                         -- 对应的地理位置ID
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    
    -- 约束
    CONSTRAINT PK_ADMIN_CODES PRIMARY KEY (ID),
    CONSTRAINT UK_ADMIN_CODE UNIQUE (COUNTRY_CODE, ADMIN_LEVEL, ADMIN_CODE),
    CONSTRAINT CK_ADMIN_LEVEL CHECK (ADMIN_LEVEL BETWEEN 1 AND 5)
);

-- =====================================================================================
-- 4. 创建辅助表：GEONAMES_COUNTRIES
-- 描述: 存储国家基本信息
-- =====================================================================================

CREATE TABLE GEONAMES_COUNTRIES (
    -- 主键和标识字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID
    ISO_CODE            CHAR(2)         NOT NULL,           -- ISO-3166 2位代码
    ISO3_CODE           CHAR(3),                            -- ISO-3166 3位代码
    ISO_NUMERIC         NUMBER(3),                          -- ISO数字代码
    FIPS_CODE           VARCHAR2(2),                        -- FIPS代码
    
    -- 国家名称
    COUNTRY_NAME        NVARCHAR2(200)  NOT NULL,           -- 国家名称
    CAPITAL             NVARCHAR2(200),                     -- 首都
    
    -- 地理信息
    AREA_SQKM           NUMBER(12,2),                       -- 面积(平方公里)
    POPULATION          NUMBER(12),                         -- 人口
    CONTINENT           CHAR(2),                            -- 大洲代码
    TLD                 VARCHAR2(10),                       -- 顶级域名
    CURRENCY_CODE       CHAR(3),                            -- 货币代码
    CURRENCY_NAME       VARCHAR2(50),                       -- 货币名称
    
    -- 通信信息
    PHONE_PREFIX        VARCHAR2(20),                       -- 电话区号
    POSTAL_CODE_FORMAT  VARCHAR2(100),                      -- 邮编格式
    POSTAL_CODE_REGEX   VARCHAR2(200),                      -- 邮编正则表达式
    
    -- 语言信息
    LANGUAGES           VARCHAR2(200),                      -- 语言列表
    GEONAME_ID          NUMBER(12),                         -- 对应的地理位置ID
    
    -- 边界信息
    NORTH_BOUND         NUMBER(8,5),                        -- 北边界纬度
    SOUTH_BOUND         NUMBER(8,5),                        -- 南边界纬度
    EAST_BOUND          NUMBER(8,5),                        -- 东边界经度
    WEST_BOUND          NUMBER(8,5),                        -- 西边界经度
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    
    -- 约束
    CONSTRAINT PK_COUNTRIES PRIMARY KEY (ID),
    CONSTRAINT UK_COUNTRY_ISO UNIQUE (ISO_CODE),
    CONSTRAINT CK_CONTINENT CHECK (CONTINENT IN ('AF','AS','EU','NA','OC','SA','AN'))
);

-- =====================================================================================
-- 5. 创建辅助表：GEONAMES_FEATURE_CODES
-- 描述: 存储特征代码说明信息
-- =====================================================================================

CREATE TABLE GEONAMES_FEATURE_CODES (
    -- 主键和标识字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID
    FEATURE_CLASS       CHAR(1)         NOT NULL,           -- 特征类别
    FEATURE_CODE        VARCHAR2(10)    NOT NULL,           -- 特征代码
    
    -- 说明信息
    NAME                VARCHAR2(100)   NOT NULL,           -- 特征名称
    DESCRIPTION         VARCHAR2(500),                      -- 特征描述
    LANGUAGE_CODE       VARCHAR2(5)     DEFAULT 'en',       -- 语言代码
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    
    -- 约束
    CONSTRAINT PK_FEATURE_CODES PRIMARY KEY (ID),
    CONSTRAINT UK_FEATURE_CODE UNIQUE (FEATURE_CLASS, FEATURE_CODE, LANGUAGE_CODE),
    CONSTRAINT CK_FC_FEATURE_CLASS CHECK (FEATURE_CLASS IN ('A','H','L','P','R','S','T','U','V'))
);

-- =====================================================================================
-- 6. 创建辅助表：GEONAMES_TIMEZONES
-- 描述: 存储时区信息
-- =====================================================================================

CREATE TABLE GEONAMES_TIMEZONES (
    -- 主键和标识字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID
    COUNTRY_CODE        CHAR(2)         NOT NULL,           -- 国家代码
    TIMEZONE_ID         VARCHAR2(40)    NOT NULL,           -- 时区标识符
    
    -- 时区信息
    GMT_OFFSET          NUMBER(4,2),                        -- GMT偏移量(1月1日)
    DST_OFFSET          NUMBER(4,2),                        -- 夏令时偏移量(7月1日)
    RAW_OFFSET          NUMBER(4,2),                        -- 原始偏移量(不含夏令时)
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    
    -- 约束
    CONSTRAINT PK_TIMEZONES PRIMARY KEY (ID),
    CONSTRAINT UK_TIMEZONE UNIQUE (COUNTRY_CODE, TIMEZONE_ID)
);

-- =====================================================================================
-- 7. 创建辅助表：GEONAMES_HIERARCHY
-- 描述: 存储地理位置层级关系
-- =====================================================================================

CREATE TABLE GEONAMES_HIERARCHY (
    -- 主键和关联字段
    ID                  NUMBER(12)      NOT NULL,           -- 内部主键ID
    PARENT_ID           NUMBER(12)      NOT NULL,           -- 父级地理位置ID
    CHILD_ID            NUMBER(12)      NOT NULL,           -- 子级地理位置ID
    HIERARCHY_TYPE      VARCHAR2(10)    NOT NULL,           -- 层级类型(ADM/其他)
    
    -- 数据管理字段
    CREATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录创建时间
    UPDATED_DATE        DATE            DEFAULT SYSDATE,    -- 记录更新时间
    
    -- 约束
    CONSTRAINT PK_HIERARCHY PRIMARY KEY (ID),
    CONSTRAINT UK_HIERARCHY UNIQUE (PARENT_ID, CHILD_ID, HIERARCHY_TYPE),
    CONSTRAINT FK_HIERARCHY_PARENT FOREIGN KEY (PARENT_ID) REFERENCES GEONAMES_LOCATIONS(GEONAME_ID),
    CONSTRAINT FK_HIERARCHY_CHILD FOREIGN KEY (CHILD_ID) REFERENCES GEONAMES_LOCATIONS(GEONAME_ID)
);

-- =====================================================================================
-- 添加表注释
-- =====================================================================================

COMMENT ON TABLE GEONAMES_LOCATIONS IS '全球地理位置数据表 - 存储来自GeoNames数据库的全球地理位置信息';
COMMENT ON TABLE GEONAMES_ALTERNATE_NAMES IS '地理位置别名表 - 存储地理位置的多语言别名、历史名称、简称等信息';
COMMENT ON TABLE GEONAMES_ADMIN_CODES IS '行政区划代码表 - 存储各级行政区划的代码和名称对照信息';
COMMENT ON TABLE GEONAMES_COUNTRIES IS '国家信息表 - 存储世界各国的基本信息、地理边界、货币语言等详细信息';
COMMENT ON TABLE GEONAMES_FEATURE_CODES IS '特征代码表 - 存储地理特征的分类代码和说明信息';
COMMENT ON TABLE GEONAMES_TIMEZONES IS '时区信息表 - 存储各国时区的详细信息和偏移量';
COMMENT ON TABLE GEONAMES_HIERARCHY IS '地理层级关系表 - 存储地理位置之间的父子层级关系';

-- =====================================================================================
-- 创建序列用于主键自增
-- =====================================================================================

CREATE SEQUENCE SEQ_GEONAMES_LOCATIONS_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;
CREATE SEQUENCE SEQ_GEONAMES_ALTERNATE_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;
CREATE SEQUENCE SEQ_GEONAMES_ADMIN_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;
CREATE SEQUENCE SEQ_GEONAMES_COUNTRIES_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;
CREATE SEQUENCE SEQ_GEONAMES_FEATURES_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;
CREATE SEQUENCE SEQ_GEONAMES_TIMEZONES_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;
CREATE SEQUENCE SEQ_GEONAMES_HIERARCHY_ID START WITH 1 INCREMENT BY 1 NOMAXVALUE NOCYCLE CACHE 1000;

-- =====================================================================================
-- 创建触发器实现主键自增和更新时间自动维护
-- =====================================================================================

-- 主表触发器
CREATE OR REPLACE TRIGGER TRG_GEONAMES_LOCATIONS_BI
    BEFORE INSERT ON GEONAMES_LOCATIONS
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_LOCATIONS_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

CREATE OR REPLACE TRIGGER TRG_GEONAMES_LOCATIONS_BU
    BEFORE UPDATE ON GEONAMES_LOCATIONS
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- 别名表触发器
CREATE OR REPLACE TRIGGER TRG_ALTERNATE_NAMES_BI
    BEFORE INSERT ON GEONAMES_ALTERNATE_NAMES
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_ALTERNATE_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- 行政区表触发器
CREATE OR REPLACE TRIGGER TRG_ADMIN_CODES_BI
    BEFORE INSERT ON GEONAMES_ADMIN_CODES
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_ADMIN_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- 国家表触发器
CREATE OR REPLACE TRIGGER TRG_COUNTRIES_BI
    BEFORE INSERT ON GEONAMES_COUNTRIES
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_COUNTRIES_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- 特征代码表触发器
CREATE OR REPLACE TRIGGER TRG_FEATURE_CODES_BI
    BEFORE INSERT ON GEONAMES_FEATURE_CODES
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_FEATURES_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- 时区表触发器
CREATE OR REPLACE TRIGGER TRG_TIMEZONES_BI
    BEFORE INSERT ON GEONAMES_TIMEZONES
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_TIMEZONES_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- 层级关系表触发器
CREATE OR REPLACE TRIGGER TRG_HIERARCHY_BI
    BEFORE INSERT ON GEONAMES_HIERARCHY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_GEONAMES_HIERARCHY_ID.NEXTVAL;
    END IF;
    :NEW.CREATED_DATE := SYSDATE;
    :NEW.UPDATED_DATE := SYSDATE;
END;
/

-- =====================================================================================
-- 创建索引以提高查询性能
-- =====================================================================================

-- 主表索引
CREATE INDEX IDX_GEONAMES_COORDINATES ON GEONAMES_LOCATIONS (LATITUDE, LONGITUDE);
CREATE INDEX IDX_GEONAMES_COUNTRY ON GEONAMES_LOCATIONS (COUNTRY_CODE);
CREATE INDEX IDX_GEONAMES_FEATURE ON GEONAMES_LOCATIONS (FEATURE_CLASS, FEATURE_CODE);
CREATE INDEX IDX_GEONAMES_NAME ON GEONAMES_LOCATIONS (NAME);
CREATE INDEX IDX_GEONAMES_ASCII_NAME ON GEONAMES_LOCATIONS (ASCII_NAME);
CREATE INDEX IDX_GEONAMES_ADMIN1 ON GEONAMES_LOCATIONS (COUNTRY_CODE, ADMIN1_CODE);
CREATE INDEX IDX_GEONAMES_ADMIN2 ON GEONAMES_LOCATIONS (COUNTRY_CODE, ADMIN1_CODE, ADMIN2_CODE);
CREATE INDEX IDX_GEONAMES_POPULATION ON GEONAMES_LOCATIONS (POPULATION);
CREATE INDEX IDX_GEONAMES_TIMEZONE ON GEONAMES_LOCATIONS (TIMEZONE);
CREATE INDEX IDX_GEONAMES_MOD_DATE ON GEONAMES_LOCATIONS (MODIFICATION_DATE);

-- 别名表索引
CREATE INDEX IDX_ALTERNATE_GEONAME ON GEONAMES_ALTERNATE_NAMES (GEONAME_ID);
CREATE INDEX IDX_ALTERNATE_LANGUAGE ON GEONAMES_ALTERNATE_NAMES (ISO_LANGUAGE);
CREATE INDEX IDX_ALTERNATE_NAME ON GEONAMES_ALTERNATE_NAMES (ALTERNATE_NAME);
CREATE INDEX IDX_ALTERNATE_PREFERRED ON GEONAMES_ALTERNATE_NAMES (IS_PREFERRED_NAME);

-- 行政区表索引
CREATE INDEX IDX_ADMIN_COUNTRY ON GEONAMES_ADMIN_CODES (COUNTRY_CODE);
CREATE INDEX IDX_ADMIN_LEVEL ON GEONAMES_ADMIN_CODES (ADMIN_LEVEL);
CREATE INDEX IDX_ADMIN_CODE ON GEONAMES_ADMIN_CODES (ADMIN_CODE);

-- 国家表索引
CREATE INDEX IDX_COUNTRY_NAME ON GEONAMES_COUNTRIES (COUNTRY_NAME);
CREATE INDEX IDX_COUNTRY_CONTINENT ON GEONAMES_COUNTRIES (CONTINENT);

-- 特征代码表索引
CREATE INDEX IDX_FEATURE_CLASS ON GEONAMES_FEATURE_CODES (FEATURE_CLASS);
CREATE INDEX IDX_FEATURE_LANGUAGE ON GEONAMES_FEATURE_CODES (LANGUAGE_CODE);

-- 时区表索引
CREATE INDEX IDX_TIMEZONE_COUNTRY ON GEONAMES_TIMEZONES (COUNTRY_CODE);

-- 层级关系表索引
CREATE INDEX IDX_HIERARCHY_PARENT ON GEONAMES_HIERARCHY (PARENT_ID);
CREATE INDEX IDX_HIERARCHY_CHILD ON GEONAMES_HIERARCHY (CHILD_ID);
CREATE INDEX IDX_HIERARCHY_TYPE ON GEONAMES_HIERARCHY (HIERARCHY_TYPE);

-- =====================================================================================
-- 创建视图用于常用查询
-- =====================================================================================

-- 城市视图（人口大于1000的居住地）
CREATE OR REPLACE VIEW V_GEONAMES_CITIES AS
SELECT
    l.ID,
    l.GEONAME_ID,
    l.NAME,
    l.ASCII_NAME,
    l.LATITUDE,
    l.LONGITUDE,
    l.COUNTRY_CODE,
    c.COUNTRY_NAME,
    l.ADMIN1_CODE,
    l.ADMIN2_CODE,
    l.POPULATION,
    l.ELEVATION,
    l.TIMEZONE,
    l.MODIFICATION_DATE
FROM GEONAMES_LOCATIONS l
LEFT JOIN GEONAMES_COUNTRIES c ON l.COUNTRY_CODE = c.ISO_CODE
WHERE l.FEATURE_CLASS = 'P'
  AND l.FEATURE_CODE IN ('PPL', 'PPLA', 'PPLA2', 'PPLA3', 'PPLA4', 'PPLC')
  AND (l.POPULATION IS NULL OR l.POPULATION >= 1000);

-- 地标建筑视图
CREATE OR REPLACE VIEW V_GEONAMES_LANDMARKS AS
SELECT
    l.ID,
    l.GEONAME_ID,
    l.NAME,
    l.ASCII_NAME,
    l.LATITUDE,
    l.LONGITUDE,
    l.FEATURE_CLASS,
    l.FEATURE_CODE,
    fc.NAME AS FEATURE_NAME,
    l.COUNTRY_CODE,
    c.COUNTRY_NAME,
    l.ADMIN1_CODE,
    l.TIMEZONE
FROM GEONAMES_LOCATIONS l
LEFT JOIN GEONAMES_COUNTRIES c ON l.COUNTRY_CODE = c.ISO_CODE
LEFT JOIN GEONAMES_FEATURE_CODES fc ON l.FEATURE_CLASS = fc.FEATURE_CLASS
    AND l.FEATURE_CODE = fc.FEATURE_CODE
    AND fc.LANGUAGE_CODE = 'en'
WHERE l.FEATURE_CODE IN ('HTL', 'MTRO', 'AIRP', 'TMPL', 'UNIV', 'HOSP', 'MUS');

-- 国家统计视图
CREATE OR REPLACE VIEW V_GEONAMES_COUNTRY_STATS AS
SELECT
    l.COUNTRY_CODE,
    c.COUNTRY_NAME,
    c.CONTINENT,
    COUNT(*) AS TOTAL_LOCATIONS,
    COUNT(CASE WHEN l.FEATURE_CLASS = 'P' THEN 1 END) AS CITIES_COUNT,
    COUNT(CASE WHEN l.FEATURE_CLASS = 'S' THEN 1 END) AS BUILDINGS_COUNT,
    COUNT(CASE WHEN l.FEATURE_CLASS = 'T' THEN 1 END) AS TERRAIN_COUNT,
    COUNT(CASE WHEN l.FEATURE_CLASS = 'H' THEN 1 END) AS WATER_COUNT,
    SUM(NVL(l.POPULATION, 0)) AS TOTAL_POPULATION,
    AVG(l.ELEVATION) AS AVG_ELEVATION,
    MIN(l.LATITUDE) AS MIN_LATITUDE,
    MAX(l.LATITUDE) AS MAX_LATITUDE,
    MIN(l.LONGITUDE) AS MIN_LONGITUDE,
    MAX(l.LONGITUDE) AS MAX_LONGITUDE
FROM GEONAMES_LOCATIONS l
LEFT JOIN GEONAMES_COUNTRIES c ON l.COUNTRY_CODE = c.ISO_CODE
GROUP BY l.COUNTRY_CODE, c.COUNTRY_NAME, c.CONTINENT
ORDER BY TOTAL_LOCATIONS DESC;

-- 多语言地名视图
CREATE OR REPLACE VIEW V_GEONAMES_MULTILINGUAL AS
SELECT
    l.GEONAME_ID,
    l.NAME AS PRIMARY_NAME,
    l.ASCII_NAME,
    l.COUNTRY_CODE,
    an.ISO_LANGUAGE,
    an.ALTERNATE_NAME,
    an.IS_PREFERRED_NAME,
    an.IS_SHORT_NAME,
    an.IS_COLLOQUIAL,
    an.IS_HISTORIC
FROM GEONAMES_LOCATIONS l
INNER JOIN GEONAMES_ALTERNATE_NAMES an ON l.GEONAME_ID = an.GEONAME_ID
WHERE an.ISO_LANGUAGE IS NOT NULL
ORDER BY l.GEONAME_ID, an.ISO_LANGUAGE;

-- =====================================================================================
-- 创建数据导入辅助存储过程
-- =====================================================================================

-- 批量导入地理位置数据的存储过程
CREATE OR REPLACE PROCEDURE SP_IMPORT_GEONAMES_BATCH(
    p_batch_size IN NUMBER DEFAULT 10000,
    p_commit_interval IN NUMBER DEFAULT 1000
) AS
BEGIN
    DBMS_OUTPUT.PUT_LINE('开始批量导入GeoNames数据...');
    DBMS_OUTPUT.PUT_LINE('批次大小: ' || p_batch_size);
    DBMS_OUTPUT.PUT_LINE('提交间隔: ' || p_commit_interval);

    -- 这里可以添加具体的导入逻辑
    -- 建议使用外部表或SQL*Loader进行大批量数据导入

    DBMS_OUTPUT.PUT_LINE('批量导入完成！');
END;
/

-- 数据质量检查存储过程
CREATE OR REPLACE PROCEDURE SP_CHECK_DATA_QUALITY AS
    v_total_count NUMBER;
    v_missing_coords NUMBER;
    v_invalid_coords NUMBER;
    v_missing_names NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('开始数据质量检查...');
    DBMS_OUTPUT.PUT_LINE('=' * 50);

    -- 总记录数
    SELECT COUNT(*) INTO v_total_count FROM GEONAMES_LOCATIONS;
    DBMS_OUTPUT.PUT_LINE('总记录数: ' || v_total_count);

    -- 缺失坐标
    SELECT COUNT(*) INTO v_missing_coords
    FROM GEONAMES_LOCATIONS
    WHERE LATITUDE IS NULL OR LONGITUDE IS NULL;
    DBMS_OUTPUT.PUT_LINE('缺失坐标: ' || v_missing_coords);

    -- 无效坐标
    SELECT COUNT(*) INTO v_invalid_coords
    FROM GEONAMES_LOCATIONS
    WHERE LATITUDE NOT BETWEEN -90 AND 90
       OR LONGITUDE NOT BETWEEN -180 AND 180;
    DBMS_OUTPUT.PUT_LINE('无效坐标: ' || v_invalid_coords);

    -- 缺失名称
    SELECT COUNT(*) INTO v_missing_names
    FROM GEONAMES_LOCATIONS
    WHERE NAME IS NULL OR TRIM(NAME) = '';
    DBMS_OUTPUT.PUT_LINE('缺失名称: ' || v_missing_names);

    DBMS_OUTPUT.PUT_LINE('数据质量检查完成！');
END;
/

-- =====================================================================================
-- 输出创建完成信息
-- =====================================================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
    DBMS_OUTPUT.PUT_LINE('GeoNames完整数据库表创建完成！');
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
    DBMS_OUTPUT.PUT_LINE('已创建的对象:');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('📊 数据表 (7个):');
    DBMS_OUTPUT.PUT_LINE('  1. GEONAMES_LOCATIONS - 地理位置主表');
    DBMS_OUTPUT.PUT_LINE('  2. GEONAMES_ALTERNATE_NAMES - 多语言别名表');
    DBMS_OUTPUT.PUT_LINE('  3. GEONAMES_ADMIN_CODES - 行政区划代码表');
    DBMS_OUTPUT.PUT_LINE('  4. GEONAMES_COUNTRIES - 国家信息表');
    DBMS_OUTPUT.PUT_LINE('  5. GEONAMES_FEATURE_CODES - 特征代码表');
    DBMS_OUTPUT.PUT_LINE('  6. GEONAMES_TIMEZONES - 时区信息表');
    DBMS_OUTPUT.PUT_LINE('  7. GEONAMES_HIERARCHY - 地理层级关系表');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('🔢 序列 (7个): SEQ_GEONAMES_*_ID');
    DBMS_OUTPUT.PUT_LINE('⚡ 触发器 (7个): TRG_*_BI (自动主键和时间戳)');
    DBMS_OUTPUT.PUT_LINE('📇 索引 (30+个): 覆盖所有主要查询字段');
    DBMS_OUTPUT.PUT_LINE('👁️ 视图 (4个): 城市、地标、国家统计、多语言');
    DBMS_OUTPUT.PUT_LINE('🔧 存储过程 (2个): 批量导入、数据质量检查');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('📥 数据导入建议:');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('1. 主表数据导入:');
    DBMS_OUTPUT.PUT_LINE('   - allCountries.txt -> GEONAMES_LOCATIONS');
    DBMS_OUTPUT.PUT_LINE('   - 或按国家导入: CN.txt, JP.txt, US.txt等');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('2. 辅助表数据导入:');
    DBMS_OUTPUT.PUT_LINE('   - alternateNamesV2.txt -> GEONAMES_ALTERNATE_NAMES');
    DBMS_OUTPUT.PUT_LINE('   - admin1CodesASCII.txt -> GEONAMES_ADMIN_CODES');
    DBMS_OUTPUT.PUT_LINE('   - admin2Codes.txt -> GEONAMES_ADMIN_CODES');
    DBMS_OUTPUT.PUT_LINE('   - countryInfo.txt -> GEONAMES_COUNTRIES');
    DBMS_OUTPUT.PUT_LINE('   - featureCodes_en.txt -> GEONAMES_FEATURE_CODES');
    DBMS_OUTPUT.PUT_LINE('   - timeZones.txt -> GEONAMES_TIMEZONES');
    DBMS_OUTPUT.PUT_LINE('   - hierarchy.txt -> GEONAMES_HIERARCHY');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('3. 导入工具建议:');
    DBMS_OUTPUT.PUT_LINE('   - 大文件: 使用SQL*Loader或外部表');
    DBMS_OUTPUT.PUT_LINE('   - 中等文件: 使用SQL*Plus或PL/SQL');
    DBMS_OUTPUT.PUT_LINE('   - 小文件: 直接使用INSERT语句');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('4. 性能优化建议:');
    DBMS_OUTPUT.PUT_LINE('   - 导入前禁用索引和触发器');
    DBMS_OUTPUT.PUT_LINE('   - 使用NOLOGGING模式');
    DBMS_OUTPUT.PUT_LINE('   - 增加SORT_AREA_SIZE和DB_BLOCK_SIZE');
    DBMS_OUTPUT.PUT_LINE('   - 导入后重建索引和统计信息');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('5. 数据验证:');
    DBMS_OUTPUT.PUT_LINE('   - 执行: EXEC SP_CHECK_DATA_QUALITY;');
    DBMS_OUTPUT.PUT_LINE('   - 检查外键约束');
    DBMS_OUTPUT.PUT_LINE('   - 验证数据完整性');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('🎯 使用示例查询:');
    DBMS_OUTPUT.PUT_LINE('   - SELECT * FROM V_GEONAMES_CITIES WHERE COUNTRY_CODE = ''CN'';');
    DBMS_OUTPUT.PUT_LINE('   - SELECT * FROM V_GEONAMES_LANDMARKS WHERE FEATURE_CODE = ''HTL'';');
    DBMS_OUTPUT.PUT_LINE('   - SELECT * FROM V_GEONAMES_COUNTRY_STATS ORDER BY TOTAL_LOCATIONS DESC;');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
    DBMS_OUTPUT.PUT_LINE('数据库表结构创建完成！可以开始导入GeoNames数据了。');
    DBMS_OUTPUT.PUT_LINE('=====================================================================================');
END;
/
