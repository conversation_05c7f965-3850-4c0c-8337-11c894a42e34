#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准表 vs 业务友好表对比分析脚本
连接Oracle数据库，分析两个表的数据差异和业务价值
"""

import cx_Oracle
import pandas as pd
from datetime import datetime
import json

class TableComparisonAnalyzer:
    """表对比分析器"""
    
    def __init__(self, connection_string):
        self.connection_string = connection_string
        self.connection = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = cx_Oracle.connect(self.connection_string)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.connection:
            self.connection.close()
            print("✅ 数据库连接已关闭")
    
    def analyze_data_comparison(self):
        """分析数据对比"""
        print("\n📊 数据对比分析")
        print("=" * 50)
        
        try:
            # 查询对比数据
            query = "SELECT * FROM V_DATA_COMPARISON ORDER BY TABLE_TYPE"
            df = pd.read_sql(query, self.connection)
            
            print("数据统计对比:")
            for _, row in df.iterrows():
                print(f"\n{row['TABLE_TYPE']} 表:")
                print(f"  • 总记录数: {row['RECORD_COUNT']:,}")
                print(f"  • 国家数量: {row['COUNTRY_COUNT']}")
                print(f"  • 特征类别数: {row['FEATURE_CLASS_COUNT']}")
                print(f"  • 有人口记录: {row['HAS_POPULATION_COUNT']:,}")
                print(f"  • 平均人口: {row['AVG_POPULATION']:,.0f}" if row['AVG_POPULATION'] else "  • 平均人口: N/A")
            
            return df
            
        except Exception as e:
            print(f"❌ 数据对比分析失败: {e}")
            return None
    
    def analyze_business_value(self):
        """分析业务价值对比"""
        print("\n🎯 业务价值对比分析")
        print("=" * 50)
        
        try:
            # 查询业务价值演示数据
            query = """
            SELECT * FROM V_BUSINESS_VALUE_DEMO 
            WHERE ROWNUM <= 10
            ORDER BY BUSINESS_IMPORTANCE_SCORE DESC NULLS LAST
            """
            df = pd.read_sql(query, self.connection)
            
            print("标准数据 vs 业务友好数据对比示例:")
            print("-" * 80)
            
            for i, row in df.iterrows():
                print(f"\n示例 {i+1}:")
                print(f"标准格式:")
                print(f"  名称: {row['STANDARD_NAME']}")
                print(f"  特征: {row['STANDARD_FEATURE']}")
                print(f"  国家: {row['STANDARD_COUNTRY']}")
                print(f"  行政区: {row['STANDARD_ADMIN1']}")
                print(f"  人口: {row['STANDARD_POPULATION']}")
                
                print(f"业务友好格式:")
                print(f"  名称: {row['BUSINESS_NAME']}")
                print(f"  特征: {row['BUSINESS_FEATURE_CN']}")
                print(f"  国家: {row['BUSINESS_COUNTRY_CN']}")
                print(f"  完整路径: {row['BUSINESS_FULL_PATH']}")
                print(f"  人口: {row['BUSINESS_POPULATION']}")
                print(f"  业务分类: {row['BUSINESS_CATEGORY']}")
                print(f"  重要性评分: {row['BUSINESS_IMPORTANCE_SCORE']}")
                print(f"  城市类型: {row['CITY_TYPE']}")
                print(f"  旅游类型: {row['TOURIST_TYPE']}")
                print("-" * 40)
            
            return df
            
        except Exception as e:
            print(f"❌ 业务价值分析失败: {e}")
            return None
    
    def analyze_field_comparison(self):
        """分析字段对比"""
        print("\n📋 字段结构对比分析")
        print("=" * 50)
        
        try:
            # 查询标准表字段
            standard_query = """
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, COMMENTS
            FROM USER_TAB_COLUMNS utc
            LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME 
                AND utc.COLUMN_NAME = ucc.COLUMN_NAME
            WHERE utc.TABLE_NAME = 'GEONAMES_STANDARD'
            ORDER BY COLUMN_ID
            """
            
            # 查询业务友好表字段
            business_query = """
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, COMMENTS
            FROM USER_TAB_COLUMNS utc
            LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME 
                AND utc.COLUMN_NAME = ucc.COLUMN_NAME
            WHERE utc.TABLE_NAME = 'GEONAMES_BUSINESS_FRIENDLY'
            ORDER BY COLUMN_ID
            """
            
            standard_fields = pd.read_sql(standard_query, self.connection)
            business_fields = pd.read_sql(business_query, self.connection)
            
            print(f"标准表字段数: {len(standard_fields)}")
            print(f"业务友好表字段数: {len(business_fields)}")
            print(f"增加字段数: {len(business_fields) - len(standard_fields)}")
            
            # 找出新增字段
            standard_columns = set(standard_fields['COLUMN_NAME'].tolist())
            business_columns = set(business_fields['COLUMN_NAME'].tolist())
            new_columns = business_columns - standard_columns
            
            print(f"\n新增的业务友好字段 ({len(new_columns)}个):")
            for col in sorted(new_columns):
                field_info = business_fields[business_fields['COLUMN_NAME'] == col].iloc[0]
                print(f"  • {col}: {field_info['DATA_TYPE']} - {field_info['COMMENTS'] or '无注释'}")
            
            return standard_fields, business_fields
            
        except Exception as e:
            print(f"❌ 字段对比分析失败: {e}")
            return None, None
    
    def analyze_business_categories(self):
        """分析业务分类统计"""
        print("\n🏷️ 业务分类统计分析")
        print("=" * 50)
        
        try:
            # 业务分类统计
            category_query = """
            SELECT 
                BUSINESS_CATEGORY,
                COUNT(*) as COUNT,
                ROUND(AVG(IMPORTANCE_SCORE), 1) as AVG_IMPORTANCE,
                COUNT(CASE WHEN IS_MAJOR_CITY = 'Y' THEN 1 END) as MAJOR_CITIES,
                COUNT(CASE WHEN IS_TOURIST_ATTRACTION = 'Y' THEN 1 END) as TOURIST_SPOTS
            FROM GEONAMES_BUSINESS_FRIENDLY
            GROUP BY BUSINESS_CATEGORY
            ORDER BY COUNT DESC
            """
            
            df = pd.read_sql(category_query, self.connection)
            
            print("业务分类统计:")
            for _, row in df.iterrows():
                print(f"\n{row['BUSINESS_CATEGORY']}:")
                print(f"  • 数量: {row['COUNT']:,}")
                print(f"  • 平均重要性: {row['AVG_IMPORTANCE']}")
                print(f"  • 主要城市: {row['MAJOR_CITIES']}")
                print(f"  • 旅游景点: {row['TOURIST_SPOTS']}")
            
            return df
            
        except Exception as e:
            print(f"❌ 业务分类分析失败: {e}")
            return None
    
    def analyze_country_distribution(self):
        """分析国家分布"""
        print("\n🌍 国家分布分析")
        print("=" * 50)
        
        try:
            # 国家分布统计
            country_query = """
            SELECT 
                COUNTRY_CODE,
                COUNTRY_NAME,
                COUNTRY_NAME_CN,
                CONTINENT_NAME,
                COUNT(*) as LOCATION_COUNT,
                COUNT(CASE WHEN IS_MAJOR_CITY = 'Y' THEN 1 END) as MAJOR_CITIES,
                ROUND(AVG(IMPORTANCE_SCORE), 1) as AVG_IMPORTANCE
            FROM GEONAMES_BUSINESS_FRIENDLY
            GROUP BY COUNTRY_CODE, COUNTRY_NAME, COUNTRY_NAME_CN, CONTINENT_NAME
            ORDER BY LOCATION_COUNT DESC
            FETCH FIRST 10 ROWS ONLY
            """
            
            df = pd.read_sql(country_query, self.connection)
            
            print("TOP10 国家数据分布:")
            for _, row in df.iterrows():
                country_display = f"{row['COUNTRY_NAME_CN']} ({row['COUNTRY_CODE']})" if row['COUNTRY_NAME_CN'] else f"{row['COUNTRY_NAME']} ({row['COUNTRY_CODE']})"
                print(f"\n{country_display}:")
                print(f"  • 地点数量: {row['LOCATION_COUNT']:,}")
                print(f"  • 主要城市: {row['MAJOR_CITIES']}")
                print(f"  • 平均重要性: {row['AVG_IMPORTANCE']}")
                print(f"  • 所属大洲: {row['CONTINENT_NAME']}")
            
            return df
            
        except Exception as e:
            print(f"❌ 国家分布分析失败: {e}")
            return None
    
    def generate_comparison_report(self):
        """生成完整对比报告"""
        print("🚀 生成标准表 vs 业务友好表对比报告")
        print("=" * 80)
        
        if not self.connect():
            return False
        
        try:
            report_data = {
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'analysis_results': {}
            }
            
            # 1. 数据对比分析
            data_comparison = self.analyze_data_comparison()
            if data_comparison is not None:
                report_data['analysis_results']['data_comparison'] = data_comparison.to_dict('records')
            
            # 2. 业务价值分析
            business_value = self.analyze_business_value()
            if business_value is not None:
                report_data['analysis_results']['business_value_samples'] = business_value.to_dict('records')
            
            # 3. 字段对比分析
            standard_fields, business_fields = self.analyze_field_comparison()
            if standard_fields is not None and business_fields is not None:
                report_data['analysis_results']['field_comparison'] = {
                    'standard_field_count': len(standard_fields),
                    'business_field_count': len(business_fields),
                    'added_field_count': len(business_fields) - len(standard_fields)
                }
            
            # 4. 业务分类分析
            business_categories = self.analyze_business_categories()
            if business_categories is not None:
                report_data['analysis_results']['business_categories'] = business_categories.to_dict('records')
            
            # 5. 国家分布分析
            country_distribution = self.analyze_country_distribution()
            if country_distribution is not None:
                report_data['analysis_results']['country_distribution'] = country_distribution.to_dict('records')
            
            # 保存报告
            report_file = f'table_comparison_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n📄 对比报告已保存: {report_file}")
            
            # 生成总结
            self.print_summary()
            
            return True
            
        finally:
            self.disconnect()
    
    def print_summary(self):
        """打印总结"""
        print("\n🎯 对比分析总结")
        print("=" * 50)
        print("✅ 业务友好表的核心价值:")
        print("  1. 🌍 多语言支持 - 中英文国家名称对照")
        print("  2. 🏷️ 业务分类 - 智能的业务场景分类")
        print("  3. 📊 重要性评分 - 基于多维度的智能评分")
        print("  4. 🎯 业务标签 - 主要城市、首都、旅游景点等标识")
        print("  5. 📍 完整路径 - 结构化的行政区层级信息")
        print("  6. 💰 商业信息 - 货币、语言等国际化信息")
        print("  7. 📈 格式化显示 - 用户友好的数据展示")
        print("  8. 🔍 查询优化 - 针对业务场景的索引优化")
        
        print("\n💡 应用场景对比:")
        print("标准表适用于:")
        print("  • 数据仓库存储")
        print("  • 技术开发调试")
        print("  • 数据备份归档")
        
        print("业务友好表适用于:")
        print("  • 用户界面展示")
        print("  • 业务逻辑处理")
        print("  • API接口服务")
        print("  • 报表分析统计")
        print("  • 多语言应用")

def main():
    """主函数"""
    print("标准表 vs 业务友好表对比分析工具")
    print("=" * 60)
    
    # 获取数据库连接
    connection_string = input("请输入Oracle连接字符串 (如 username/password@localhost:1521/xe): ").strip()
    if not connection_string:
        print("❌ 连接字符串不能为空")
        return
    
    # 创建分析器并执行
    analyzer = TableComparisonAnalyzer(connection_string)
    
    try:
        analyzer.generate_comparison_report()
        
        print("\n🎉 分析完成！")
        print("现在您可以:")
        print("  1. 查看生成的JSON报告文件")
        print("  2. 在数据库中执行查询验证结果")
        print("  3. 基于分析结果优化业务应用")
        
    except KeyboardInterrupt:
        print("\n❌ 用户取消操作")
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")

if __name__ == "__main__":
    main()
