#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeoNames数据分析脚本
分析香港(HK)地区的地理数据
"""

import pandas as pd
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_geonames_data(filename='HK.txt'):
    """
    分析GeoNames数据文件
    """
    print("=" * 80)
    print("GeoNames 香港地区数据分析报告")
    print("=" * 80)
    
    # 根据readme.txt定义列名
    columns = [
        'geonameid',        # 地理名称ID
        'name',             # 地名（UTF-8）
        'asciiname',        # ASCII地名
        'alternatenames',   # 别名（逗号分隔）
        'latitude',         # 纬度（WGS84）
        'longitude',        # 经度（WGS84）
        'feature_class',    # 特征类别
        'feature_code',     # 特征代码
        'country_code',     # 国家代码（ISO-3166）
        'cc2',              # 备用国家代码
        'admin1_code',      # 一级行政区代码
        'admin2_code',      # 二级行政区代码
        'admin3_code',      # 三级行政区代码
        'admin4_code',      # 四级行政区代码
        'population',       # 人口
        'elevation',        # 海拔（米）
        'dem',              # 数字高程模型
        'timezone',         # 时区
        'modification_date' # 修改日期
    ]
    
    try:
        # 读取数据文件
        df = pd.read_csv(filename, sep='\t', names=columns, encoding='utf-8')
        
        print(f"数据文件: {filename}")
        print(f"总记录数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print()
        
        # 基本统计信息
        print("1. 数据基本信息")
        print("-" * 40)
        print(f"数据形状: {df.shape}")
        print(f"内存使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        print()
        
        # 显示前几行数据
        print("2. 数据样本（前5行）")
        print("-" * 40)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        print(df.head())
        print()
        
        # 特征类别分析
        print("3. 特征类别(Feature Class)分析")
        print("-" * 40)
        feature_class_counts = df['feature_class'].value_counts()
        print("特征类别统计:")
        for fc, count in feature_class_counts.items():
            print(f"  {fc}: {count} 个地点")
        print()
        
        # 特征代码分析
        print("4. 特征代码(Feature Code)分析")
        print("-" * 40)
        feature_code_counts = df['feature_code'].value_counts().head(10)
        print("前10个最常见的特征代码:")
        for fc, count in feature_code_counts.items():
            print(f"  {fc}: {count} 个地点")
        print()
        
        # 行政区分析
        print("5. 行政区分析")
        print("-" * 40)
        admin1_counts = df['admin1_code'].value_counts()
        print("一级行政区统计:")
        for admin, count in admin1_counts.items():
            if pd.notna(admin) and admin != '':
                print(f"  {admin}: {count} 个地点")
        print()
        
        # 人口统计
        print("6. 人口统计")
        print("-" * 40)
        population_data = df[df['population'] > 0]['population']
        if len(population_data) > 0:
            print(f"有人口数据的地点: {len(population_data)} 个")
            print(f"总人口: {population_data.sum():,}")
            print(f"平均人口: {population_data.mean():.0f}")
            print(f"人口中位数: {population_data.median():.0f}")
            print(f"最大人口: {population_data.max():,}")
            print(f"最小人口: {population_data.min():,}")
        else:
            print("没有人口数据")
        print()
        
        # 海拔统计
        print("7. 海拔统计")
        print("-" * 40)
        elevation_data = df[df['elevation'] != 0]['elevation']
        if len(elevation_data) > 0:
            print(f"有海拔数据的地点: {len(elevation_data)} 个")
            print(f"平均海拔: {elevation_data.mean():.1f} 米")
            print(f"海拔中位数: {elevation_data.median():.1f} 米")
            print(f"最高海拔: {elevation_data.max()} 米")
            print(f"最低海拔: {elevation_data.min()} 米")
        else:
            print("没有海拔数据")
        print()
        
        # 地理坐标范围
        print("8. 地理坐标范围")
        print("-" * 40)
        print(f"纬度范围: {df['latitude'].min():.6f} 到 {df['latitude'].max():.6f}")
        print(f"经度范围: {df['longitude'].min():.6f} 到 {df['longitude'].max():.6f}")
        print()
        
        # 时区信息
        print("9. 时区信息")
        print("-" * 40)
        timezone_counts = df['timezone'].value_counts()
        print("时区统计:")
        for tz, count in timezone_counts.items():
            print(f"  {tz}: {count} 个地点")
        print()
        
        # 数据质量检查
        print("10. 数据质量检查")
        print("-" * 40)
        print("缺失值统计:")
        missing_data = df.isnull().sum()
        for col, missing in missing_data.items():
            if missing > 0:
                print(f"  {col}: {missing} 个缺失值 ({missing/len(df)*100:.1f}%)")
        print()
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def create_visualizations(df):
    """
    创建数据可视化图表
    """
    if df is None:
        return
    
    print("11. 创建可视化图表")
    print("-" * 40)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('香港地区GeoNames数据可视化分析', fontsize=16)
    
    # 1. 特征类别分布
    feature_class_counts = df['feature_class'].value_counts()
    axes[0, 0].pie(feature_class_counts.values, labels=feature_class_counts.index, autopct='%1.1f%%')
    axes[0, 0].set_title('特征类别分布')
    
    # 2. 人口分布（对数尺度）
    population_data = df[df['population'] > 0]['population']
    if len(population_data) > 0:
        axes[0, 1].hist(np.log10(population_data), bins=20, alpha=0.7)
        axes[0, 1].set_title('人口分布（对数尺度）')
        axes[0, 1].set_xlabel('log10(人口)')
        axes[0, 1].set_ylabel('频次')
    
    # 3. 地理位置散点图
    axes[1, 0].scatter(df['longitude'], df['latitude'], alpha=0.6, s=10)
    axes[1, 0].set_title('地理位置分布')
    axes[1, 0].set_xlabel('经度')
    axes[1, 0].set_ylabel('纬度')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 特征代码前10名
    feature_code_counts = df['feature_code'].value_counts().head(10)
    axes[1, 1].barh(range(len(feature_code_counts)), feature_code_counts.values)
    axes[1, 1].set_yticks(range(len(feature_code_counts)))
    axes[1, 1].set_yticklabels(feature_code_counts.index)
    axes[1, 1].set_title('前10个特征代码')
    axes[1, 1].set_xlabel('数量')
    
    plt.tight_layout()
    plt.savefig('hk_geonames_analysis.png', dpi=300, bbox_inches='tight')
    print("图表已保存为: hk_geonames_analysis.png")
    plt.show()

if __name__ == "__main__":
    # 分析数据
    df = analyze_geonames_data('HK.txt')
    
    # 创建可视化
    if df is not None:
        create_visualizations(df)
        
        # 保存处理后的数据
        df.to_csv('HK_processed.csv', index=False, encoding='utf-8-sig')
        print("处理后的数据已保存为: HK_processed.csv")
